{"name": "send-visit-notification-digest-email", "workflowId": "send-visit-notification-digest-email", "description": "Imported from Novu platform", "active": true, "tags": [], "steps": [{"type": "digest", "name": "Digest Step", "active": true, "shouldStopOnFail": false, "uuid": "step_0_1754332575123", "controlValues": {"amount": 35, "unit": "minutes", "digestKey": "", "cron": ""}, "filters": []}, {"type": "email", "name": "Email Step", "active": true, "shouldStopOnFail": false, "uuid": "step_1_1754332575124", "controlValues": {"editorType": "html", "disableOutputSanitization": false, "body": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta charset=\"UTF-8\">\n  <title>Visit Scheduled</title>\n  <style>\n    /* Reset & basic styles */\n    body, table, td, p {\n      margin: 0;\n      padding: 0;\n      font-family: Arial, sans-serif;\n      color: #333333;\n    }\n    body {\n      background-color: #f9f9f9;\n      padding: 20px;\n    }\n    .container {\n      max-width: 600px;\n      margin: 0 auto;\n      background-color: #ffffff;\n      border: 1px solid #e0e0e0;\n      border-radius: 4px;\n    }\n    .header {\n      padding: 20px;\n      text-align: center;\n      border-bottom: 1px solid #e0e0e0;\n    }\n    .content {\n      padding: 20px;\n      line-height: 1.5;\n    }\n    .guest-table {\n      width: 100%;\n      border-collapse: collapse;\n      margin-top: 15px;\n    }\n    .guest-table th,\n    .guest-table td {\n      border: 1px solid #e0e0e0;\n      padding: 8px;\n      text-align: left;\n    }\n    .guest-table th {\n      background-color: #f1f1f1;\n    }\n    .footer {\n      padding: 15px 20px;\n      font-size: 12px;\n      color: #888888;\n      border-top: 1px solid #e0e0e0;\n      text-align: center;\n    }\n  </style>\n</head>\n<body>\n  <table class=\"container\" cellpadding=\"0\" cellspacing=\"0\" role=\"presentation\">\n    <tr>\n      <td class=\"header\">\n        <h2>Visit Scheduled</h2>\n      </td>\n    </tr>\n    <tr>\n      <td class=\"content\">\n        <p>{{payload.title}}</p></br>\n        <p>Hi {{payload.host_name}},</p>\n        <p>\n          We have scheduled a meeting with you on\n          <strong>{{payload.start_date}}</strong>.\n        </p>\n\n        <p>Below are the details of the guests who will be joining:</p>\n        <ul>\n          {%for item in steps.digest-step.events %}\n            {% for extraData in item.payload.extra_data %}\n              <li>{{extraData.guest_first_name}}</li>\n            {%  endfor %}\n          {% endfor %}\n        </ul>\n        <p>\n          If you have any questions or need to reschedule, please let us know.\n        </p>\n        <p>Looking forward to seeing you,</p>\n        <p>The Team</p>\n      </td>\n    </tr>\n    <tr>\n      <td class=\"footer\">\n        &copy;  2025 Your Company, Inc. All rights reserved.\n      </td>\n    </tr>\n  </table>\n</body>\n</html>\n"}, "filters": []}], "validatePayload": true, "payloadSchema": {"type": "object", "properties": {"title": {"type": "string"}}}, "isTranslationEnabled": false, "preferences": {"user": null, "default": {"all": {"enabled": true, "readOnly": false}, "channels": {"email": {"enabled": true}, "sms": {"enabled": true}, "in_app": {"enabled": true}, "chat": {"enabled": true}, "push": {"enabled": true}}}}, "originalId": "6889b5db52f3d8ae7761cee7", "slug": "send-visit-notification-digest-email_wf_g4jX49alrZoaiaAB", "origin": "novu-cloud", "status": "ERROR", "createdAt": "2025-07-30T06:04:11.162Z", "updatedAt": "2025-08-04T04:16:19.021Z", "lastTriggeredAt": "2025-07-30T06:13:23.911Z", "updatedBy": {"_id": "6889b3bfba353d7f425b4b05", "externalId": "user_30a9dCuIGOvUfdHEZ7XAEXutgNY"}, "payloadExample": {"title": "Software Engineer"}, "importedAt": "2025-08-04T18:36:15.133Z", "source": "imported_from_platform", "lastUpdated": "2025-08-04T18:36:15.133Z", "filePath": "C:\\Users\\<USER>\\caremate\\api\\storage\\imported-workflows\\send-visit-notification-digest-email.json"}