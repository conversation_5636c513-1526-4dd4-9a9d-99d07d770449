"use strict";
const { v4: uuidv4 } = require("uuid");
const { faker } = require("@faker-js/faker");

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // Specific identity ID for testing
      const testIdentityId = "46bd626b-2b26-440e-9570-b391704eeab7";
      
      // Check if identity already exists
      const existingIdentity = await queryInterface.sequelize.query(
        "SELECT identity_id FROM identity WHERE identity_id = :identity_id",
        {
          replacements: { identity_id: testIdentityId },
          type: Sequelize.QueryTypes.SELECT,
          transaction
        }
      );

      // Get a sample facility for the identity
      const facilities = await queryInterface.sequelize.query(
        "SELECT facility_id FROM facility LIMIT 1;",
        { type: Sequelize.QueryTypes.SELECT, transaction }
      );
      const facility_id = facilities.length > 0 ? facilities[0].facility_id : null;

      // Create identity if it doesn't exist
      if (existingIdentity.length === 0) {
        await queryInterface.bulkInsert(
          "identity",
          [
            {
              identity_id: testIdentityId,
              facility_id: facility_id,
              email: "<EMAIL>",
              first_name: "John",
              last_name: "Doe",
              middle_name: "Michael",
              eid: "EMP001",
              identity_type: 0, // Employee type
              national_id: "*********",
              mobile: "+*********0",
              start_date: new Date("2024-01-01"),
              end_date: null,
              status: 0, // Active
              suspension: false,
              suspension_date: null,
              reason: null,
              image: null,
              company: "Caremate Technologies",
              organization: "IT Department",
              company_code: "CARE001",
              job_title: "Software Developer",
              job_code: "DEV001",
              manager: null,
              updated_by: null,
              created_at: new Date(),
              updated_at: new Date(),
            },
          ],
          { transaction }
        );
      }

      // Create Vehicle records
      const vehicles = [
        {
          vehicle_id: uuidv4(),
          plate_number: "ABC-123",
          issued_by: "Department of Motor Vehicles",
          vin: "1HGBH41JXMN109186",
          year: 2023,
          make: "Toyota",
          model: "Camry",
          color: "Blue",
          uploaded_date: new Date("2024-01-15"),
          identity_id: testIdentityId,
          created_by: testIdentityId,
          updated_by: testIdentityId,
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          vehicle_id: uuidv4(),
          plate_number: "XYZ-789",
          issued_by: "Department of Motor Vehicles",
          vin: "2T1BURHE0JC123456",
          year: 2022,
          make: "Honda",
          model: "Civic",
          color: "Red",
          uploaded_date: new Date("2024-02-01"),
          identity_id: testIdentityId,
          created_by: testIdentityId,
          updated_by: testIdentityId,
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];

      await queryInterface.bulkInsert("vehicle", vehicles, { transaction });

      // Create Delegates records
      const delegates = [
        {
          delegate_id: uuidv4(),
          name: "Jane Smith",
          eid: "DEL001",
          task_to_delegate: "Approve purchase orders up to $5,000",
          start_date: new Date("2024-01-01"),
          end_date: new Date("2024-12-31"),
          status: 0, // Active
          identity_id: testIdentityId,
          created_by: testIdentityId,
          updated_by: testIdentityId,
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          delegate_id: uuidv4(),
          name: "Robert Johnson",
          eid: "DEL002",
          task_to_delegate: "Review and approve time-off requests",
          start_date: new Date("2024-03-01"),
          end_date: null,
          status: 0, // Active
          identity_id: testIdentityId,
          created_by: testIdentityId,
          updated_by: testIdentityId,
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          delegate_id: uuidv4(),
          name: "Sarah Wilson",
          eid: "DEL003",
          task_to_delegate: "Handle customer service escalations",
          start_date: new Date("2024-02-15"),
          end_date: new Date("2024-08-15"),
          status: 1, // Inactive
          identity_id: testIdentityId,
          created_by: testIdentityId,
          updated_by: testIdentityId,
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];

      await queryInterface.bulkInsert("delegates", delegates, { transaction });

      // Create Card records
      const cards = [
        {
          card_id: uuidv4(),
          identity_id: testIdentityId,
          card_number: "CARD00*********0",
          card_format: 0, // HIDICLASS
          facility_code: 1001,
          pin: 1234,
          template: 0, // Employee
          active_date: new Date("2024-01-01"),
          deactive_date: null,
          reason: "Initial card issuance",
          status: 0, // Active
          updated_by: testIdentityId,
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          card_id: uuidv4(),
          identity_id: testIdentityId,
          card_number: "CARD987654321098",
          card_format: 1, // HID
          facility_code: 1002,
          pin: 5678,
          template: 0, // Employee
          active_date: new Date("2024-03-01"),
          deactive_date: new Date("2024-12-31"),
          reason: "Temporary access card",
          status: 2, // Pending Assignment
          updated_by: testIdentityId,
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];

      await queryInterface.bulkInsert("card", cards, { transaction });

      await transaction.commit();
      console.log("✅ Sample profile test data seeded successfully for identity:", testIdentityId);
    } catch (error) {
      await transaction.rollback();
      console.error("❌ Error seeding sample profile test data:", error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      const testIdentityId = "46bd626b-2b26-440e-9570-b391704eeab7";

      // Delete in reverse order due to foreign key constraints
      await queryInterface.bulkDelete("card", { identity_id: testIdentityId }, { transaction });
      await queryInterface.bulkDelete("delegates", { identity_id: testIdentityId }, { transaction });
      await queryInterface.bulkDelete("vehicle", { identity_id: testIdentityId }, { transaction });
      await queryInterface.bulkDelete("identity", { identity_id: testIdentityId }, { transaction });

      await transaction.commit();
      console.log("✅ Sample profile test data removed successfully for identity:", testIdentityId);
    } catch (error) {
      await transaction.rollback();
      console.error("❌ Error removing sample profile test data:", error);
      throw error;
    }
  },
};
