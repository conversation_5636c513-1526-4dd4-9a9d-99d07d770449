{"name": "Onboarding Demo Workflow", "workflowId": "onboarding-demo-workflow", "description": "A demo workflow to showcase the Inbox component", "active": true, "tags": [], "steps": [{"type": "in_app", "name": "Inbox", "active": true, "shouldStopOnFail": false, "uuid": "step_0_1754332575142", "controlValues": {"subject": "**In-App Notification Subject**", "body": "Welcome to Novu! This is a demo notification to showcase the Inbox component. You can customize the content, styling and actions of notifications to match your app's needs.", "avatar": "https://dashboard-v2.novu.co/images/novu.svg", "primaryAction": {"label": "Add to your app", "redirect": {"target": "_blank", "url": "/onboarding/inbox/embed"}}}, "filters": []}], "validatePayload": true, "payloadSchema": {"type": "object", "additionalProperties": true, "properties": {}}, "isTranslationEnabled": false, "preferences": {"user": null, "default": {"all": {"enabled": true, "readOnly": false}, "channels": {"email": {"enabled": true}, "sms": {"enabled": true}, "in_app": {"enabled": true}, "chat": {"enabled": true}, "push": {"enabled": true}}}}, "originalId": "6889b3c5ae236f7a451bb4da", "slug": "onboarding-demo-workflow_wf_g4jU0yly1X34saQE", "origin": "novu-cloud", "status": "ACTIVE", "createdAt": "2025-07-30T05:55:17.094Z", "updatedAt": "2025-08-04T04:16:17.879Z", "lastTriggeredAt": "2025-07-30T05:56:03.707Z", "updatedBy": {"_id": "6889b3bfba353d7f425b4b05", "externalId": "user_30a9dCuIGOvUfdHEZ7XAEXutgNY"}, "payloadExample": {}, "importedAt": "2025-08-04T18:36:15.146Z", "source": "imported_from_platform", "lastUpdated": "2025-08-04T18:36:15.146Z", "filePath": "C:\\Users\\<USER>\\caremate\\api\\storage\\imported-workflows\\onboarding-demo-workflow.json"}