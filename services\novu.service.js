const config = require('../config/config');
const logger = require('../config/logger');

class NovuService {
  constructor() {
    this.apiUrl = 'https://api.novu.co/v1/events/trigger';
    this.subscribersUrl = 'https://api.novu.co/v1/subscribers';
    this.workflowsUrl = 'https://api.novu.co/v2/workflows';
    this.apiKey = config.novu.apiKey;
    this.isEnabled = config.novu.enabled;

    if (this.isEnabled) {
      this.init();
    }
  }

  init() {
    try {
      if (!this.apiKey) {
        throw new Error('Novu API key is required when Novu is enabled');
      }

      const queueArg = process.argv.find((arg) => arg.startsWith("--queue="));
      const isNotificationQueue = queueArg && queueArg.includes('notification');
      
      if (isNotificationQueue) {
        logger.info('Novu service initialized successfully with REST API');
      }
      // logger.info('Novu service initialized successfully with REST API');
    } catch (error) {
      logger.error('Failed to initialize Novu service:', error.message);
      this.isEnabled = false;
    }
  }

  isNovuEnabled() {
    return this.isEnabled && this.apiKey;
  }

  async createOrUpdateSubscriber(subscriberId, subscriberData) {
    if (!this.isNovuEnabled()) {
      throw new Error('Novu service is not enabled or initialized');
    }

    try {
      // Prepare subscriber data with subscriberId
      const payload = {
        subscriberId: subscriberId,
        ...subscriberData
      };

      // Use POST to /subscribers for creating/updating (Novu handles both)
      const response = await fetch(this.subscribersUrl, {
        method: 'POST',
        headers: {
          'Authorization': `ApiKey ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(`Novu API error: ${response.status} - ${JSON.stringify(result)}`);
      }

      logger.info(`Subscriber ${subscriberId} created/updated successfully`);
      return result;
    } catch (error) {
      logger.error(`Error creating/updating subscriber ${subscriberId}:`, error.message);
      throw error;
    }
  }

  // async triggerNotification(workflowId, subscriberId, payload, options = {}) {
  //   if (!this.isNovuEnabled()) {
  //     throw new Error('Novu service is not enabled or initialized');
  //   }

  //   try {
  //     const result = await this.novu.trigger(workflowId, {
  //       to: {
  //         subscriberId: subscriberId,
  //       },
  //       payload: payload,
  //     }, options.transactionId);

  //     logger.info(`Notification triggered successfully for workflow ${workflowId} to subscriber ${subscriberId}`);
  //     return result;
  //   } catch (error) {
  //     logger.error(`Error triggering notification for workflow ${workflowId}:`, error.message);
  //     throw error;
  //   }
  // }

  async sendNotification(notificationData) {
    if (!this.isNovuEnabled()) {
      logger.warn('Novu is disabled, skipping notification');
      return null;
    }

    try {
      const {
        workflowId,
        subscriberId,
        subscriberData,
        payload,
        transactionId
      } = notificationData;

      // Create or update subscriber if subscriber data is provided
      if (subscriberData) {
        await this.createOrUpdateSubscriber(subscriberId, subscriberData);
      }

      // Prepare the trigger request payload
      const requestPayload = {
        name: workflowId,
        to: {
          subscriberId: subscriberId,
        },
        payload: payload,
      };

      if (transactionId) {
        requestPayload.transactionId = transactionId;
      }

      // Trigger the notification via REST API
      const response = await fetch(this.apiUrl, {
        method: 'POST',
        headers: {
          'Authorization': `ApiKey ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestPayload)
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(`Novu API error: ${response.status} - ${JSON.stringify(result)}`);
      }

      logger.info(`Notification triggered successfully for workflow ${workflowId} to subscriber ${subscriberId}`);
      return result;
    } catch (error) {
      logger.error('Error sending Novu notification:', error.message);
      throw error;
    }
  }

  async sendDigestNotification(notificationData){
    if (!this.isNovuEnabled()) {
      logger.warn('Novu is disabled, skipping notification');
      return null;
    }
    if(!config.novu.digestWorkflowId){
      logger.warn('Novu digest workflow ID is not configured, skipping notification');
      return null;
    }

    const {transactionId, subscriberId, subscriberData, payload } = notificationData;

    return this.sendNotification({
      workflowId: config?.novu?.digestWorkflowId,
      subscriberId,
      subscriberData,
      payload,
      transactionId
    });
  }

  // Workflow Management Methods

  /**
   * List all workflows with optional filtering and pagination
   * @param {Object} options - Query options for filtering workflows
   * @returns {Promise<Object>} List of workflows with metadata
   */
  async listWorkflows(options = {}) {
    if (!this.isNovuEnabled()) {
      throw new Error('Novu service is not enabled or initialized');
    }

    try {
      const queryParams = new URLSearchParams();

      if (options.limit) queryParams.append('limit', options.limit);
      if (options.offset) queryParams.append('offset', options.offset);
      if (options.orderDirection) queryParams.append('orderDirection', options.orderDirection);
      if (options.orderBy) queryParams.append('orderBy', options.orderBy);
      if (options.query) queryParams.append('query', options.query);
      if (options.tags && Array.isArray(options.tags)) {
        options.tags.forEach(tag => queryParams.append('tags', tag));
      }
      if (options.status && Array.isArray(options.status)) {
        options.status.forEach(status => queryParams.append('status', status));
      }

      const url = `${this.workflowsUrl}?${queryParams.toString()}`;

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `ApiKey ${this.apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(`Novu API error: ${response.status} - ${JSON.stringify(result)}`);
      }

      logger.info(`Retrieved ${result.workflows?.length || 0} workflows from Novu`);
      return result;
    } catch (error) {
      logger.error('Error listing Novu workflows:', error.message);
      throw error;
    }
  }

  /**
   * Retrieve a specific workflow by ID
   * @param {string} workflowId - The workflow identifier
   * @returns {Promise<Object>} Workflow details
   */
  async getWorkflow(workflowId) {
    if (!this.isNovuEnabled()) {
      throw new Error('Novu service is not enabled or initialized');
    }

    try {
      const response = await fetch(`${this.workflowsUrl}/${workflowId}`, {
        method: 'GET',
        headers: {
          'Authorization': `ApiKey ${this.apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(`Novu API error: ${response.status} - ${JSON.stringify(result)}`);
      }

      logger.info(`Retrieved workflow ${workflowId} from Novu`);
      return result;
    } catch (error) {
      logger.error(`Error retrieving workflow ${workflowId}:`, error.message);
      throw error;
    }
  }

  /**
   * Create a new workflow
   * @param {Object} workflowData - Workflow configuration data
   * @returns {Promise<Object>} Created workflow details
   */
  async createWorkflow(workflowData) {
    if (!this.isNovuEnabled()) {
      throw new Error('Novu service is not enabled or initialized');
    }

    try {
      const response = await fetch(this.workflowsUrl, {
        method: 'POST',
        headers: {
          'Authorization': `ApiKey ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(workflowData)
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(`Novu API error: ${response.status} - ${JSON.stringify(result)}`);
      }

      logger.info(`Created workflow ${result.workflowId} in Novu`);
      return result;
    } catch (error) {
      logger.error(`Error creating Novu workflow: ${error.message}`);
      logger.error(`Workflow data that failed: ${JSON.stringify(workflowData, null, 2)}`);
      throw error;
    }
  }

  /**
   * Update an existing workflow
   * @param {string} workflowId - The workflow identifier
   * @param {Object} workflowData - Updated workflow configuration data
   * @returns {Promise<Object>} Updated workflow details
   */
  async updateWorkflow(workflowId, workflowData) {
    if (!this.isNovuEnabled()) {
      throw new Error('Novu service is not enabled or initialized');
    }

    try {
      const response = await fetch(`${this.workflowsUrl}/${workflowId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `ApiKey ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(workflowData)
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(`Novu API error: ${response.status} - ${JSON.stringify(result)}`);
      }

      logger.info(`Updated workflow ${workflowId} in Novu`);
      return result;
    } catch (error) {
      logger.error(`Error updating workflow ${workflowId}:`, error.message);
      throw error;
    }
  }

  /**
   * Delete a workflow
   * @param {string} workflowId - The workflow identifier
   * @returns {Promise<boolean>} Success status
   */
  async deleteWorkflow(workflowId) {
    if (!this.isNovuEnabled()) {
      throw new Error('Novu service is not enabled or initialized');
    }

    try {
      const response = await fetch(`${this.workflowsUrl}/${workflowId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `ApiKey ${this.apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const result = await response.json();
        throw new Error(`Novu API error: ${response.status} - ${JSON.stringify(result)}`);
      }

      logger.info(`Deleted workflow ${workflowId} from Novu`);
      return true;
    } catch (error) {
      logger.error(`Error deleting workflow ${workflowId}:`, error.message);
      throw error;
    }
  }

  /**
   * Sync a workflow (useful for external workflows)
   * @param {string} workflowId - The workflow identifier
   * @param {Object} syncData - Sync configuration data
   * @returns {Promise<Object>} Sync result
   */
  async syncWorkflow(workflowId, syncData = {}) {
    if (!this.isNovuEnabled()) {
      throw new Error('Novu service is not enabled or initialized');
    }

    try {
      const response = await fetch(`${this.workflowsUrl}/${workflowId}/sync`, {
        method: 'PUT',
        headers: {
          'Authorization': `ApiKey ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(syncData)
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(`Novu API error: ${response.status} - ${JSON.stringify(result)}`);
      }

      logger.info(`Synced workflow ${workflowId} in Novu`);
      return result;
    } catch (error) {
      logger.error(`Error syncing workflow ${workflowId}:`, error.message);
      throw error;
    }
  }

  // File Storage Methods

  /**
   * Save workflow to consolidated workflows file
   * @param {Object} workflowData - Cleaned workflow data
   * @returns {Promise<Object>} Saved workflow with file path
   */
  async saveWorkflowToFile(workflowData) {
    const fs = require('fs').promises;
    const path = require('path');

    try {
      // Create workflows directory if it doesn't exist
      const workflowsDir = path.join(process.cwd(), 'storage', 'imported-workflows');
      await fs.mkdir(workflowsDir, { recursive: true });
      console.log('workflowsDir', workflowData);
      // Use individual file for each workflow
      const fileName = `${workflowData.workflowId}.json`;
      const filePath = path.join(workflowsDir, fileName);

      // Create workflow object with metadata
      const workflowToSave = {
        ...workflowData,
        importedAt: new Date().toISOString(),
        source: 'imported_from_platform',
        lastUpdated: new Date().toISOString(),
        filePath: filePath
      }

      await fs.writeFile(filePath, JSON.stringify(workflowToSave, null, 2), 'utf8');

      logger.info(`Saved workflow ${workflowData.workflowId} to file: ${filePath}`);
      return workflowToSave;
    } catch (error) {
      logger.error(`Error saving workflow to consolidated file: ${error.message}`);
      throw error;
    }
  }

  /**
   * Load workflow from individual file
   * @param {string} workflowId - Workflow ID to load
   * @returns {Promise<Object>} Loaded workflow data
   */
  async loadWorkflowFromFile(workflowId) {
    const fs = require('fs').promises;
    const path = require('path');

    try {
      const workflowsDir = path.join(process.cwd(), 'storage', 'imported-workflows');
      const filePath = path.join(workflowsDir, `${workflowId}.json`);
      console.log("workflow dir", workflowsDir);
      console.log("workflow file", filePath);

      // Read individual workflow file
      const fileContent = await fs.readFile(filePath, 'utf8');
      const workflow = JSON.parse(fileContent);
      console.log("file content", fileContent);
      console.log("workflow data", workflow);

      logger.info(`Loaded workflow ${workflowId} from file: ${filePath}`);
      return workflow;
    } catch (error) {
      logger.error(`Error loading workflow from file: ${error.message}`);
      throw error;
    }
  }

  /**
   * List all imported workflows from consolidated file
   * @returns {Promise<Array>} Array of imported workflows
   */
  async listImportedWorkflows(workflowId) {
    const fs = require('fs').promises;
    const path = require('path');

    try {
      const workflowsDir = path.join(process.cwd(), 'storage', 'imported-workflows');
      const filePath = path.join(workflowsDir, `${workflowId}.json`);
      console.log("workflow dir", workflowsDir, filePath, workflowId);
      // Check if file exists
      try {
        await fs.access(filePath);
      } catch {
        return []; // File doesn't exist, return empty array
      }

      // Read consolidated file
      const fileContent = await fs.readFile(filePath, 'utf8');
      const data = JSON.parse(fileContent);

      // Transform workflows for listing
      const workflows = data.workflows.map(workflow => ({
        workflowId: workflow.workflowId,
        name: workflow.name,
        description: workflow.description,
        active: workflow.active,
        tags: workflow.tags,
        importedAt: workflow.importedAt,
        lastUpdated: workflow.lastUpdated,
        filePath: filePath,
        stepsCount: workflow.steps?.length || 0
      }));

      logger.info(`Found ${workflows.length} imported workflows in consolidated file`);
      return workflows;
    } catch (error) {
      logger.error(`Error listing imported workflows: ${error.message}`);
      throw error;
    }
  }

  /**
   * Delete imported workflow from consolidated file
   * @param {string} workflowId - Workflow ID to delete
   * @returns {Promise<boolean>} Success status
   */
  async deleteImportedWorkflow(workflowId) {
    const fs = require('fs').promises;
    const path = require('path');

    try {
      const workflowsDir = path.join(process.cwd(), 'storage', 'imported-workflows');
      const filePath = path.join(workflowsDir, 'imported-workflows.json');

      // Read consolidated file
      const fileContent = await fs.readFile(filePath, 'utf8');
      const data = JSON.parse(fileContent);

      // Find and remove workflow
      const workflowIndex = data.workflows.findIndex(w => w.workflowId === workflowId);

      if (workflowIndex === -1) {
        throw new Error(`Workflow not found in consolidated file for ID: ${workflowId}`);
      }

      // Remove workflow from array
      data.workflows.splice(workflowIndex, 1);

      // Update metadata
      data.lastUpdated = new Date().toISOString();
      data.totalWorkflows = data.workflows.length;

      // Save updated file
      await fs.writeFile(filePath, JSON.stringify(data, null, 2), 'utf8');

      logger.info(`Deleted workflow ${workflowId} from consolidated file: ${filePath}`);
      return true;
    } catch (error) {
      logger.error(`Error deleting imported workflow: ${error.message}`);
      throw error;
    }
  }

  // Data Cleaning and Validation Methods

  /**
   * Clean and validate workflow data for import
   * @param {Object} workflowData - Raw workflow data from platform
   * @param {string} finalWorkflowId - The final workflow ID to use
   * @returns {Object} Cleaned workflow data
   */
  cleanWorkflowDataForImport(workflowData, finalWorkflowId) {
    try {
      // Start with essential fields only
      const cleanedData = {
        name: workflowData.name || 'Imported Workflow',
        workflowId: finalWorkflowId,
        description: workflowData.description || 'Imported from Novu platform',
        active: Boolean(workflowData.active),
        tags: Array.isArray(workflowData.tags) ? workflowData.tags : [],
        steps: []
      };

      // Clean and validate steps
      if (workflowData.steps && Array.isArray(workflowData.steps)) {
        cleanedData.steps = workflowData.steps.map((step, index) => {
          const cleanedStep = {
            type: step.type || 'email',
            name: step.name || `Step ${index + 1}`,
            active: step.active !== false, // Default to true
            shouldStopOnFail: Boolean(step.shouldStopOnFail),
            uuid: step.uuid || `step_${index}_${Date.now()}`,
            controlValues: step.controlValues || {},
            filters: step.filters || []
          };

          // Clean control values
          if (step.controlValues) {
            cleanedStep.controlValues = {
              ...step.controlValues
            };

            // Remove any undefined or null values
            Object.keys(cleanedStep.controlValues).forEach(key => {
              if (cleanedStep.controlValues[key] === undefined || cleanedStep.controlValues[key] === null) {
                delete cleanedStep.controlValues[key];
              }
            });
          }

          return cleanedStep;
        });
      }

      // Add optional fields if they exist and are valid
      if (workflowData.validatePayload !== undefined) {
        cleanedData.validatePayload = Boolean(workflowData.validatePayload);
      }

      if (workflowData.payloadSchema && typeof workflowData.payloadSchema === 'object') {
        cleanedData.payloadSchema = workflowData.payloadSchema;
      }

      if (workflowData.isTranslationEnabled !== undefined) {
        cleanedData.isTranslationEnabled = Boolean(workflowData.isTranslationEnabled);
      }

      if (workflowData.preferences && typeof workflowData.preferences === 'object') {
        cleanedData.preferences = workflowData.preferences;
      }

      // Add additional detailed fields from API response
      if (workflowData._id) {
        cleanedData.originalId = workflowData._id;
      }

      if (workflowData.slug) {
        cleanedData.slug = workflowData.slug;
      }

      if (workflowData.origin) {
        cleanedData.origin = workflowData.origin;
      }

      if (workflowData.status) {
        cleanedData.status = workflowData.status;
      }

      if (workflowData.createdAt) {
        cleanedData.createdAt = workflowData.createdAt;
      }

      if (workflowData.updatedAt) {
        cleanedData.updatedAt = workflowData.updatedAt;
      }

      if (workflowData.lastPublishedAt) {
        cleanedData.lastPublishedAt = workflowData.lastPublishedAt;
      }

      if (workflowData.lastTriggeredAt) {
        cleanedData.lastTriggeredAt = workflowData.lastTriggeredAt;
      }

      if (workflowData.updatedBy) {
        cleanedData.updatedBy = workflowData.updatedBy;
      }

      if (workflowData.payloadExample) {
        cleanedData.payloadExample = workflowData.payloadExample;
      }

      logger.info(`Cleaned workflow data for ${finalWorkflowId}: ${cleanedData.steps.length} steps`);
      return cleanedData;
    } catch (error) {
      logger.error(`Error cleaning workflow data for ${finalWorkflowId}: ${error.message}`);
      // Return minimal valid workflow if cleaning fails
      return {
        name: workflowData.name || 'Imported Workflow',
        workflowId: finalWorkflowId,
        description: 'Imported from Novu platform (minimal)',
        active: false,
        tags: [],
        steps: []
      };
    }
  }

  // Platform Import Methods

  /**
   * Fetch workflows directly from Novu platform
   * @param {Object} options - Fetch options
   * @returns {Promise<Array>} Array of workflows from platform
   */
  async fetchWorkflowsFromPlatform(options = {}) {
    if (!this.isNovuEnabled()) {
      throw new Error('Novu service is not enabled or initialized');
    }

    const {
      limit = 100,
      offset = 0,
      orderDirection = 'DESC',
      orderBy = 'createdAt',
      query = '',
      tags = [],
      status = []
    } = options;

    try {
      // Use POST method with request body for complex filtering
      // This avoids query parameter array issues
      const requestBody = {
        limit,
        offset,
        orderDirection,
        orderBy
      };

      // Only add query if it's not empty
      if (query && query.trim() !== '' && query !== 'string') {
        requestBody.query = query;
      }

      // Store filter parameters for client-side filtering
      // (API doesn't support these parameters)

      logger.info(`Fetching workflows with parameters: ${JSON.stringify(requestBody)}`);

      // Use GET method with proper query parameters
      // Novu API v2 uses GET with query parameters, not POST
      const params = new URLSearchParams({
        limit: limit.toString(),
        offset: offset.toString(),
        orderDirection,
        orderBy
      });

      // Only add query if it's not empty and not the default "string" value
      if (query && query.trim() !== '' && query !== 'string') {
        params.append('query', query);
      }

      // For array parameters, we need to check if Novu accepts them
      // Skip array parameters for now since they're causing validation errors
      // TODO: Implement client-side filtering after fetching all workflows

      const finalUrl = `${this.workflowsUrl}?${params}`;
      logger.info(`Making GET request to: ${finalUrl}`);

      const response = await fetch(finalUrl, {
        method: 'GET',
        headers: {
          'Authorization': `ApiKey ${this.apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(`Novu API error: ${response.status} - ${JSON.stringify(result)}`);
      }

      // Debug the response structure
      logger.info(`Novu API response structure: ${JSON.stringify(Object.keys(result || {}))}`);

      // Handle different possible response structures
      let workflows = [];

      try {
        if (result && result.data && Array.isArray(result.data)) {
          workflows = result.data;
          logger.info('Using result.data as workflows array');
        } else if (Array.isArray(result)) {
          workflows = result;
          logger.info('Using result directly as workflows array');
        } else if (result && result.workflows && Array.isArray(result.workflows)) {
          workflows = result.workflows;
          logger.info('Using result.workflows as workflows array');
        } else if (result && result.data && result.data.workflows && Array.isArray(result.data.workflows)) {
          workflows = result.data.workflows;
          logger.info('Using result.data.workflows as workflows array');
        } else {
          logger.warn(`Unexpected response structure from Novu API. Available keys: ${Object.keys(result || {}).join(', ')}`);
          logger.warn(`Response sample: ${JSON.stringify(result).substring(0, 500)}...`);
          workflows = [];
        }
      } catch (parseError) {
        logger.error(`Error parsing Novu API response: ${parseError.message}`);
        workflows = [];
      }
      console.log('workflows length', workflows)
      logger.info(`Fetched ${workflows.length} workflows from Novu platform`);

      // Only apply filtering if we have workflows and they are in an array
      if (!Array.isArray(workflows)) {
        logger.error(`Workflows is not an array: ${typeof workflows}`);
        return [];
      }

      // Apply client-side filtering for tags and status since API doesn't support them
      const tagsArray = Array.isArray(tags) ? tags : (tags ? [tags] : []);
      const filteredTags = tagsArray.filter(tag => tag && tag.trim() !== '' && tag !== 'string');

      const statusArray = Array.isArray(status) ? status : (status ? [status] : []);
      const filteredStatus = statusArray.filter(s => s && s.trim() !== '');

      if (filteredTags.length > 0) {
        workflows = workflows.filter(workflow => {
          const workflowTags = workflow.tags || [];
          return filteredTags.some(tag => workflowTags.includes(tag));
        });
        logger.info(`Filtered by tags [${filteredTags.join(', ')}]: ${workflows.length} workflows remaining`);
      }

      if (filteredStatus.length > 0) {
        workflows = workflows.filter(workflow => {
          const workflowStatus = workflow.active ? 'ACTIVE' : 'INACTIVE';
          return filteredStatus.includes(workflowStatus);
        });
        logger.info(`Filtered by status [${filteredStatus.join(', ')}]: ${workflows.length} workflows remaining`);
      }

      return workflows;
    } catch (error) {
      logger.error('Error fetching workflows from Novu platform:', error.message);
      throw error;
    }
  }

  /**
   * Import workflows directly from Novu platform
   * @param {Object} options - Import options
   * @returns {Promise<Object>} Import results
   */
  async importWorkflowsFromPlatform(options = {}) {
    if (!this.isNovuEnabled()) {
      throw new Error('Novu service is not enabled or initialized');
    }

    const {
      fetchOptions = {},
      importOptions = {}
    } = options;

    try {
      // Fetch workflows from platform
      logger.info('Fetching workflows from Novu platform...');
      const workflows = await this.fetchWorkflowsFromPlatform(fetchOptions);
      if (!workflows || workflows.length === 0) {
        return {
          imported: [],
          skipped: [],
          errors: [],
          total: 0,
          message: 'No workflows found on platform'
        };
      }

      // Transform platform workflows to import   format
      const importData = {
        exportedAt: new Date().toISOString(),
        version: '1.0.0',
        totalWorkflows: workflows.length,
        workflows: workflows.map(workflow => ({
          ...workflow,
          originalId: workflow._id,
          originalWorkflowId: workflow.workflowId
        }))
      };
      
      // Import the workflows
      logger.info(`Importing ${workflows.length} workflows from platform...`);
      const result = await this.importWorkflows(importData, importOptions);

      logger.info(`Platform import completed: ${result.imported.length} imported, ${result.skipped.length} skipped, ${result.errors.length} errors`);
      return {
        ...result,
        source: 'platform',
        fetchedWorkflows: workflows.length
      };
    } catch (error) {
      logger.error('Error importing workflows from Novu platform:', error.message);
      throw error;
    }
  }

  // Import/Export Utility Methods

  /**
   * Export workflows to a JSON format for backup or migration
   * @param {Array<string>} workflowIds - Optional array of specific workflow IDs to export
   * @returns {Promise<Object>} Exported workflows data
   */
  async exportWorkflows(workflowIds = null) {
    if (!this.isNovuEnabled()) {
      throw new Error('Novu service is not enabled or initialized');
    }

    try {
      let workflows = [];

      if (workflowIds && Array.isArray(workflowIds)) {
        // Export specific workflows
        for (const workflowId of workflowIds) {
          const workflow = await this.getWorkflow(workflowId);
          workflows.push(workflow);
        }
      } else {
        // Export all workflows
        const result = await this.listWorkflows({ limit: 1000 }); // Adjust limit as needed
        workflows = result.workflows || [];

        // Get full details for each workflow
        const detailedWorkflows = [];
        for (const workflow of workflows) {
          const fullWorkflow = await this.getWorkflow(workflow.workflowId);
          detailedWorkflows.push(fullWorkflow);
        }
        workflows = detailedWorkflows;
      }

      const exportData = {
        exportedAt: new Date().toISOString(),
        version: '1.0',
        totalWorkflows: workflows.length,
        workflows: workflows.map(workflow => ({
          // Remove system-generated fields that shouldn't be imported
          name: workflow.name,
          description: workflow.description,
          tags: workflow.tags,
          active: workflow.active,
          validatePayload: workflow.validatePayload,
          payloadSchema: workflow.payloadSchema,
          isTranslationEnabled: workflow.isTranslationEnabled,
          workflowId: workflow.workflowId,
          steps: workflow.steps,
          preferences: workflow.preferences,
          // Keep original IDs for reference
          originalId: workflow._id,
          originalWorkflowId: workflow.workflowId
        }))
      };

      logger.info(`Exported ${workflows.length} workflows from Novu`);
      return exportData;
    } catch (error) {
      logger.error('Error exporting Novu workflows:', error.message);
      throw error;
    }
  }

  /**
   * Import workflows from exported JSON data
   * @param {Object} importData - Exported workflows data
   * @param {Object} options - Import options
   * @returns {Promise<Object>} Import results
   */
  async importWorkflows(importData, options = {}) {
    if (!this.isNovuEnabled()) {
      throw new Error('Novu service is not enabled or initialized');
    }

    const {
      overwriteExisting = false,
      skipExisting = true,
      updateWorkflowIds = true
    } = options;

    try {
      const results = {
        imported: [],
        skipped: [],
        errors: [],
        total: importData.workflows?.length || 0
      };

      if (!importData.workflows || !Array.isArray(importData.workflows)) {
        throw new Error('Invalid import data: workflows array is required');
      }

      // First, fetch all detailed workflow data
      const workflowsToProcess = [];
      for (const workflowData of importData.workflows) {
        try {
          let finalWorkflowId = workflowData.workflowId;

          // Generate new workflow ID if requested
          if (updateWorkflowIds) {
            finalWorkflowId = `${workflowData.workflowId}`;
          }

          const wholeData = await this.getWorkflow(workflowData.workflowId);
          console.log('wholeData', wholeData);

          workflowsToProcess.push({
            originalData: workflowData,
            detailedData: wholeData.data,
            finalWorkflowId: finalWorkflowId
          });
        } catch (error) {
          logger.error(`Failed to fetch workflow ${workflowData.workflowId}: ${error.message}`);
          results.errors.push({
            workflowId: workflowData.workflowId,
            error: error.message
          });
        }
      }

      // Now process all workflows for saving
      for (const workflowInfo of workflowsToProcess) {
        try {
          const { detailedData, finalWorkflowId } = workflowInfo;

          // Check if workflow file already exists
          let existingWorkflow = null;
          try {
            existingWorkflow = await this.loadWorkflowFromFile(finalWorkflowId);
          } catch (error) {
            // Workflow file doesn't exist, which is fine for import
            existingWorkflow = null;
          }

          if (existingWorkflow) {
            if (skipExisting) {
              results.skipped.push({
                workflowId: finalWorkflowId,
                reason: 'Workflow already exists and skipExisting is true'
              });
              continue;
            } else if (overwriteExisting) {
              // Update existing workflow file with detailed data from API
              const cleanedWorkflowData = this.cleanWorkflowDataForImport(detailedData, finalWorkflowId);
              const updatedWorkflow = await this.saveWorkflowToFile(cleanedWorkflowData);
              results.imported.push({
                workflowId: finalWorkflowId,
                action: 'overwritten_in_file',
                workflow: updatedWorkflow,
                filePath: updatedWorkflow.filePath,
                isUpdate: true
              });
            } else {
              results.errors.push({
                workflowId: finalWorkflowId,
                error: 'Workflow already exists and overwriteExisting is false'
              });
            }
          } else {
            // Store detailed workflow data to individual file
            const cleanedWorkflowData = this.cleanWorkflowDataForImport(detailedData, finalWorkflowId);
            const savedWorkflow = await this.saveWorkflowToFile(cleanedWorkflowData);
            results.imported.push({
              workflowId: finalWorkflowId,
              action: 'saved_to_file',
              workflow: savedWorkflow,
              filePath: savedWorkflow.filePath,
              isUpdate: false
            });
          }
        } catch (error) {
          logger.error(`Failed to import workflow ${workflowInfo.finalWorkflowId}: ${error.message}`);
          results.errors.push({
            workflowId: workflowInfo.finalWorkflowId,
            error: error.message
          });
        }
      }

      logger.info(`Import completed: ${results.imported.length} imported, ${results.skipped.length} skipped, ${results.errors.length} errors`);
      return results;
    } catch (error) {
      logger.error('Error importing Novu workflows:', error.message);
      throw error;
    }
  }
  /**
   * Export imported workflows to Novu platform
   * @param {Array<string>} workflowIds - Optional array of workflow IDs to export
   * @returns {Promise<Object>} Export results
   */
  async exportImportedWorkflowsToNovu(workflowIds = []) {
    if (!this.isNovuEnabled()) {
      throw new Error('Novu service is not enabled or initialized');
    }

    try {
      const results = {
        exported: [],
        skipped: [],
        errors: [],
        total: 0
      };

      // If specific workflow IDs provided, use them directly
      const workflowsToExport = workflowIds.length > 0 ? workflowIds : [];
      
      // If no specific IDs provided, get all imported workflows
      if (workflowsToExport.length === 0) {
        const importedWorkflows = await this.listImportedWorkflows(workflowIds);

        workflowsToExport.push(...importedWorkflows.map(w => w.workflowId));
      }
      console.log("ssss", workflowsToExport)
      results.total = workflowsToExport.length;
      
      for (const workflowId of workflowsToExport) {
        try {
          // Load full workflow data from file using workflow ID
          const workflowData = await this.loadWorkflowFromFile(workflowId);
          
          // Check if workflow already exists in Novu
          let existingWorkflow = null;
          try {
            existingWorkflow = await this.getWorkflow(workflowData.workflowId);
          } catch (error) {
            // Workflow doesn't exist in Novu, which is fine
            existingWorkflow = null;
          }

          if (existingWorkflow) {
            // Update existing workflow
            const updatedWorkflow = await this.updateWorkflow(workflowData.workflowId, workflowData);
            results.exported.push({
              workflowId: workflowData.workflowId,
              name: workflowData.name,
              action: 'updated',
              workflow: updatedWorkflow
            });
          } else {
            // Create new workflow
            const createdWorkflow = await this.createWorkflow(workflowData);
            results.exported.push({
              workflowId: workflowData.workflowId,
              name: workflowData.name,
              action: 'created',
              workflow: createdWorkflow
            });
          }
        } catch (error) {
          logger.error(`Failed to export workflow ${workflowId}: ${error.message}`);
          results.errors.push({
            workflowId: workflowId,
            error: error.message
          });
        }
      }

      logger.info(`Export to Novu completed: ${results.exported.length} exported, ${results.errors.length} errors`);
      return results;
    } catch (error) {
      logger.error('Error exporting workflows to Novu:', error.message);
      throw error;
    }
  }

}

// Create singleton instance
const novuService = new NovuService();

module.exports = novuService;
