const express = require('express');
const auth = require('../middlewares/auth');
const validate = require('../middlewares/validate');
const NovuWorkflowController = require('../controllers/novuWorkflow.controller');
const NovuWorkflowValidation = require('../validations/novuWorkflow.validation');

const router = express.Router();

/**
 * @swagger
 * /novu-workflows/export:
 *   post:
 *     summary: Export Novu workflows to JSON
 *     tags: [Novu Workflow Import/Export]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               workflowIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Optional array of specific workflow IDs to export. If not provided, all workflows will be exported.
 *           example:
 *             workflowIds: ["workflow-1", "workflow-2"]
 *     responses:
 *       200:
 *         description: Workflows exported successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Workflows exported successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     exportedAt:
 *                       type: string
 *                       format: date-time
 *                     version:
 *                       type: string
 *                       example: "1.0"
 *                     totalWorkflows:
 *                       type: integer
 *                     workflows:
 *                       type: array
 *                       items:
 *                         type: object
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post(
  '/export',
  auth('export_novu_workflows'),
  validate(NovuWorkflowValidation.exportWorkflows),
  NovuWorkflowController.exportWorkflows
);

/**
 * @swagger
 * /novu-workflows/export/bulk:
 *   get:
 *     summary: Bulk export specific Novu workflows by IDs
 *     tags: [Novu Workflow Import/Export]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: workflowIds
 *         required: true
 *         schema:
 *           type: string
 *         description: Comma-separated list of workflow IDs to export
 *         example: "workflow-1,workflow-2,workflow-3"
 *     responses:
 *       200:
 *         description: Workflows bulk exported successfully
 *         headers:
 *           Content-Disposition:
 *             description: Attachment filename for download
 *             schema:
 *               type: string
 *               example: 'attachment; filename="novu-workflows-bulk-export-1234567890.json"'
 *       400:
 *         description: Workflow IDs are required
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get(
  '/export/bulk',
  auth('export_novu_workflows'),
  validate(NovuWorkflowValidation.bulkExportWorkflows),
  NovuWorkflowController.bulkExportWorkflows
);

/**
 * @swagger
 * /novu-workflows/import:
 *   post:
 *     summary: Import Novu workflows from JSON data
 *     tags: [Novu Workflow Import/Export]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - importData
 *             properties:
 *               importData:
 *                 type: object
 *                 required:
 *                   - exportedAt
 *                   - version
 *                   - totalWorkflows
 *                   - workflows
 *                 properties:
 *                   exportedAt:
 *                     type: string
 *                     format: date-time
 *                   version:
 *                     type: string
 *                     example: "1.0"
 *                   totalWorkflows:
 *                     type: integer
 *                     minimum: 0
 *                   workflows:
 *                     type: array
 *                     items:
 *                       type: object
 *                       required:
 *                         - name
 *                         - workflowId
 *                         - steps
 *                       properties:
 *                         name:
 *                           type: string
 *                         workflowId:
 *                           type: string
 *                         description:
 *                           type: string
 *                         tags:
 *                           type: array
 *                           items:
 *                             type: string
 *                         active:
 *                           type: boolean
 *                         steps:
 *                           type: array
 *                           items:
 *                             type: object
 *               options:
 *                 type: object
 *                 properties:
 *                   overwriteExisting:
 *                     type: boolean
 *                     default: false
 *                     description: Whether to overwrite existing workflows
 *                   skipExisting:
 *                     type: boolean
 *                     default: true
 *                     description: Whether to skip existing workflows
 *                   updateWorkflowIds:
 *                     type: boolean
 *                     default: true
 *                     description: Whether to generate new workflow IDs for imported workflows
 *     responses:
 *       200:
 *         description: Workflows imported successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Workflows imported successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     imported:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           workflowId:
 *                             type: string
 *                           action:
 *                             type: string
 *                             enum: [created, updated]
 *                     skipped:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           workflowId:
 *                             type: string
 *                           reason:
 *                             type: string
 *                     errors:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           workflowId:
 *                             type: string
 *                           error:
 *                             type: string
 *                     total:
 *                       type: integer
 *       400:
 *         description: Invalid import data
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post(
  '/import',
  auth('import_novu_workflows'),
  validate(NovuWorkflowValidation.importWorkflows),
  NovuWorkflowController.importWorkflows
);

/**
 * @swagger
 * /novu-workflows/export/template:
 *   get:
 *     summary: Get workflow export template/schema
 *     tags: [Novu Workflow Import/Export]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Export template retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Export template retrieved successfully"
 *                 data:
 *                   type: object
 *                   description: Template structure for workflow export/import
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get(
  '/export/template',
  auth('view_novu_workflows'),
  NovuWorkflowController.getExportTemplate
);

module.exports = router;
