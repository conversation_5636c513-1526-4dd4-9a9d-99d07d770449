{"name": "Rich HTML Email", "workflowId": "rich-html-email", "description": "Styled HTML email with CSS", "active": true, "tags": ["html", "styled"], "steps": [{"type": "digest", "name": "Digest Step", "active": true, "shouldStopOnFail": false, "uuid": "step_0_1754332110785", "controlValues": {"amount": 42, "unit": "minutes", "digestKey": "", "cron": ""}, "filters": []}, {"type": "email", "name": "Styled Email", "active": true, "shouldStopOnFail": false, "uuid": "step_1_1754332110785", "controlValues": {"editorType": "html", "disableOutputSanitization": false, "subject": "🎉 Welcome {{subscriber.firstName}}!", "body": "<!doctype html>\n<html>\n  <head>\n    <style>\n      body{font-family:Arial,sans-serif;margin:0;padding:0;background-color:#f4f4f4;}.container{max-width:600px;margin:0 auto;background-color:white;}.header{background-color:#007bff;color:white;padding:30px;text-align:center;}.header h1{margin:0;font-size:28px;}.content{padding:30px;}.button{display:inline-block;background-color:#28a745;color:white;padding:12px 24px;text-decoration:none;border-radius:5px;margin:20px 0;}.footer{background-color:#f8f9fa;padding:20px;text-align:center;color:#666;}\n    </style>\n  </head>\n  <body>\n    <div class=\"container\">\n      <div class=\"header\">\n        <h1>Welcome {{ subscriber.firstName }}!</h1>\n      </div>\n      <div class=\"content\">\n        <h2>Your Account is Ready</h2>\n        <p>\n          Hello\n          <strong>\n            {{- subscriber.firstName }}\n            {{ subscriber.lastName -}}</strong\n          >,\n        </p>\n        <p>We're excited to have you on board! Your account has been successfully created and is ready to use.</p>\n        <div style=\"background-color:#e7f3ff;border-left:4px solid #007bff;padding:15px;margin:20px 0;\">\n          <h3>Account Details:</h3>\n          <ul>\n            <li><strong>Email:</strong> {{ subscriber.email }}</li>\n            <li><strong>User ID:</strong> {{ subscriber.subscriberId }}</li>\n            <li><strong>Registration Date:</strong> {{ payload.registrationDate }}</li>\n          </ul>\n        </div>\n        <p>Ready to get started?</p>\n        <a href=\"{{payload.dashboardUrl}}\" class=\"button\">Go to Dashboard</a>\n        <p>If you have any questions, feel free to contact our support team.</p>\n      </div>\n      <div class=\"footer\"><p>&copy; 2024 Your Company. All rights reserved.</p></div>\n    </div>\n  </body>\n</html>\n"}, "filters": []}], "validatePayload": true, "payloadSchema": {"type": "object", "additionalProperties": true, "properties": {}}, "isTranslationEnabled": false, "preferences": {"user": null, "default": {"all": {"enabled": true, "readOnly": false}, "channels": {"email": {"enabled": true}, "sms": {"enabled": true}, "in_app": {"enabled": true}, "chat": {"enabled": true}, "push": {"enabled": true}}}}, "originalId": "688b215c8def975d2ce0e5c3", "slug": "rich-html-email_wf_g4s78YQxNyGFtLud", "origin": "novu-cloud", "status": "ACTIVE", "createdAt": "2025-07-31T07:55:08.082Z", "updatedAt": "2025-08-04T06:52:23.108Z", "updatedBy": {"_id": "6889b3bfba353d7f425b4b05", "externalId": "user_30a9dCuIGOvUfdHEZ7XAEXutgNY"}, "payloadExample": {}, "importedAt": "2025-08-04T18:28:30.797Z", "source": "imported_from_platform", "lastUpdated": "2025-08-04T18:28:30.797Z", "filePath": "C:\\Users\\<USER>\\caremate\\api\\storage\\imported-workflows\\rich-html-email.json"}