const httpStatus = require("http-status");
const { sendSuccess, sendError, catchAsync } = require("../helpers/api.helper");
const novuService = require("../services/novu.service");

/**
 * @desc List all workflows with optional filtering and pagination
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {JSON} JSON object containing workflows list
 */
exports.listWorkflows = catchAsync(async (req, res) => {
  const {
    limit,
    offset,
    orderDirection,
    orderBy,
    query,
    tags,
    status
  } = req.query;

  const options = {};
  if (limit) options.limit = parseInt(limit);
  if (offset) options.offset = parseInt(offset);
  if (orderDirection) options.orderDirection = orderDirection;
  if (orderBy) options.orderBy = orderBy;
  if (query) options.query = query;
  if (tags) options.tags = Array.isArray(tags) ? tags : [tags];
  if (status) options.status = Array.isArray(status) ? status : [status];

  const result = await novuService.listWorkflows(options);
  sendSuccess(res, "Workflows retrieved successfully", httpStatus.OK, result);
});

/**
 * @desc Get a specific workflow by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {JSON} JSON object containing workflow details
 */
exports.getWorkflow = catchAsync(async (req, res) => {
  const { workflowId } = req.params;
  
  const workflow = await novuService.getWorkflow(workflowId);
  sendSuccess(res, "Workflow retrieved successfully", httpStatus.OK, workflow);
});

/**
 * @desc Create a new workflow
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {JSON} JSON object containing created workflow
 */
exports.createWorkflow = catchAsync(async (req, res) => {
  const workflowData = req.body;
  
  const workflow = await novuService.createWorkflow(workflowData);
  sendSuccess(res, "Workflow created successfully", httpStatus.CREATED, workflow);
});

/**
 * @desc Update an existing workflow
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {JSON} JSON object containing updated workflow
 */
exports.updateWorkflow = catchAsync(async (req, res) => {
  const { workflowId } = req.params;
  const workflowData = req.body;
  
  const workflow = await novuService.updateWorkflow(workflowId, workflowData);
  sendSuccess(res, "Workflow updated successfully", httpStatus.OK, workflow);
});

/**
 * @desc Delete a workflow
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {JSON} JSON object confirming deletion
 */
exports.deleteWorkflow = catchAsync(async (req, res) => {
  const { workflowId } = req.params;
  
  await novuService.deleteWorkflow(workflowId);
  sendSuccess(res, "Workflow deleted successfully", httpStatus.OK, { workflowId });
});

/**
 * @desc Sync a workflow
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {JSON} JSON object containing sync result
 */
exports.syncWorkflow = catchAsync(async (req, res) => {
  const { workflowId } = req.params;
  const syncData = req.body;
  
  const result = await novuService.syncWorkflow(workflowId, syncData);
  sendSuccess(res, "Workflow synced successfully", httpStatus.OK, result);
});

/**
 * @desc Export workflows to JSON format
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {JSON} JSON object containing exported workflows
 */
exports.exportWorkflows = catchAsync(async (req, res) => {
  const { workflowIds } = req.body; // Optional array of specific workflow IDs
  
  const exportData = await novuService.exportWorkflows(workflowIds);
  
  // Set headers for file download
  res.setHeader('Content-Type', 'application/json');
  res.setHeader('Content-Disposition', `attachment; filename="novu-workflows-export-${Date.now()}.json"`);
  
  sendSuccess(res, "Workflows exported successfully", httpStatus.OK, exportData);
});

/**
 * @desc Import workflows from JSON data
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {JSON} JSON object containing import results
 */
exports.importWorkflows = catchAsync(async (req, res) => {
  const { importData, options = {} } = req.body;

  if (!importData) {
    return sendError(res, "Import data is required", httpStatus.BAD_REQUEST);
  }

  const result = await novuService.importWorkflows(importData, options);
  sendSuccess(res, "Workflows imported successfully", httpStatus.OK, result);
});

/**
 * @desc Import workflows directly from Novu platform
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {JSON} JSON object containing import results
 */
exports.importWorkflowsFromPlatform = catchAsync(async (req, res) => {
  const { fetchOptions = {}, importOptions = {} } = req.body;

  const result = await novuService.importWorkflowsFromPlatform({
    fetchOptions,
    importOptions
  });

  sendSuccess(res, "Workflows imported from platform successfully", httpStatus.OK, result);
});

/**
 * @desc Fetch workflows from Novu platform without importing
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {JSON} JSON object containing workflows from platform
 */
exports.fetchWorkflowsFromPlatform = catchAsync(async (req, res) => {
  const options = req.query;

  const workflows = await novuService.fetchWorkflowsFromPlatform(options);
  sendSuccess(res, "Workflows fetched from platform successfully", httpStatus.OK, {
    workflows,
    total: workflows.length
  });
});

/**
 * @desc List imported workflows stored in files
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {JSON} JSON object containing imported workflows
 */
exports.listImportedWorkflows = catchAsync(async (req, res) => {
  const workflows = await novuService.listImportedWorkflows();
  sendSuccess(res, "Imported workflows retrieved successfully", httpStatus.OK, {
    workflows,
    total: workflows.length
  });
});

/**
 * @desc Get specific imported workflow from file
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {JSON} JSON object containing workflow details
 */
exports.getImportedWorkflow = catchAsync(async (req, res) => {
  const { workflowId } = req.params;

  const workflow = await novuService.loadWorkflowFromFile(workflowId);
  sendSuccess(res, "Imported workflow retrieved successfully", httpStatus.OK, workflow);
});

/**
 * @desc Delete imported workflow file
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {JSON} JSON object containing deletion result
 */
exports.deleteImportedWorkflow = catchAsync(async (req, res) => {
  const { workflowId } = req.params;

  await novuService.deleteImportedWorkflow(workflowId);
  sendSuccess(res, "Imported workflow deleted successfully", httpStatus.OK, {
    workflowId,
    deleted: true
  });
});

/**
 * @desc Bulk export workflows by IDs
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {JSON} JSON object containing exported workflows
 */
exports.bulkExportWorkflows = catchAsync(async (req, res) => {
  const { workflowIds } = req.query;
  
  if (!workflowIds) {
    return sendError(res, "Workflow IDs are required for bulk export", httpStatus.BAD_REQUEST);
  }
  
  const ids = Array.isArray(workflowIds) ? workflowIds : workflowIds.split(',');
  const exportData = await novuService.exportWorkflows(ids);
  
  // Set headers for file download
  res.setHeader('Content-Type', 'application/json');
  res.setHeader('Content-Disposition', `attachment; filename="novu-workflows-bulk-export-${Date.now()}.json"`);
  
  sendSuccess(res, "Workflows bulk exported successfully", httpStatus.OK, exportData);
});

/**
 * @desc Export imported workflows to Novu platform
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {JSON} JSON object containing export results
 */
exports.exportToNovu = catchAsync(async (req, res) => {
  const { workflowIds = [] } = req.body;

  const result = await novuService.exportImportedWorkflowsToNovu(workflowIds);
  sendSuccess(res, "Workflows exported to Novu successfully", httpStatus.OK, result);
});

/**
 * @desc Get workflow export template/schema
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {JSON} JSON object containing export template
 */
exports.getExportTemplate = catchAsync(async (req, res) => {
  const template = {
    exportedAt: "2024-01-01T00:00:00.000Z",
    version: "1.0",
    totalWorkflows: 0,
    workflows: [
      {
        name: "Example Workflow",
        description: "This is an example workflow template",
        tags: ["example", "template"],
        active: true,
        validatePayload: false,
        payloadSchema: null,
        isTranslationEnabled: false,
        workflowId: "example-workflow-id",
        steps: [
          {
            name: "Email Step",
            type: "email",
            controlValues: {
              subject: "Welcome {{subscriber.firstName}}!",
              body: "Hello {{subscriber.firstName}}, welcome to our platform!"
            }
          }
        ],
        preferences: {
          user: {
            all: { enabled: true, readOnly: false },
            channels: {
              email: { enabled: true },
              sms: { enabled: false }
            }
          }
        }
      }
    ]
  };
  
  sendSuccess(res, "Export template retrieved successfully", httpStatus.OK, template);
});
