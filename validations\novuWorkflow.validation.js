const Joi = require('joi');

// Email step control values schema
const emailControlValuesSchema = Joi.object({
  subject: Joi.string().required().description('Email subject line with variable support like {{subscriber.firstName}}'),
  body: Joi.string().required().description('Email content/message with variable support'),
  preheader: Joi.string().optional().description('Email preheader text'),
  senderName: Joi.string().optional().description('Sender name override'),
  replyTo: Joi.string().email().optional().description('Reply-to email address')
});

// SMS step control values schema
const smsControlValuesSchema = Joi.object({
  body: Joi.string().required().max(160).description('SMS message content (max 160 characters)')
});

// In-app notification control values schema
const inAppControlValuesSchema = Joi.object({
  subject: Joi.string().required().description('Notification title'),
  body: Joi.string().required().description('Notification message'),
  cta: Joi.object({
    text: Joi.string().required().description('Call-to-action button text'),
    url: Joi.string().uri().required().description('Call-to-action URL')
  }).optional().description('Call-to-action button configuration')
});

// Push notification control values schema
const pushControlValuesSchema = Joi.object({
  subject: Joi.string().required().description('Push notification title'),
  body: Joi.string().required().description('Push notification message'),
  icon: Joi.string().uri().optional().description('Notification icon URL'),
  badge: Joi.string().uri().optional().description('Notification badge URL'),
  sound: Joi.string().optional().description('Notification sound')
});

// Chat step control values schema
const chatControlValuesSchema = Joi.object({
  body: Joi.string().required().description('Chat message content')
});

// Common workflow step schema with proper controlValues validation
const workflowStepSchema = Joi.object({
  _id: Joi.string().optional(),
  name: Joi.string().required().description('Step name for identification'),
  type: Joi.string().valid(
    'in_app', 'email', 'sms', 'chat', 'push', 'digest', 'trigger', 'delay', 'custom'
  ).required().description('Step type - determines the delivery channel'),
  controlValues: Joi.when('type', {
    switch: [
      { is: 'email', then: emailControlValuesSchema },
      { is: 'sms', then: smsControlValuesSchema },
      { is: 'in_app', then: inAppControlValuesSchema },
      { is: 'push', then: pushControlValuesSchema },
      { is: 'chat', then: chatControlValuesSchema }
    ],
    otherwise: Joi.object().optional()
  }).description('Step-specific configuration values')
});

// Workflow preferences schema
const workflowPreferencesSchema = Joi.object({
  user: Joi.object({
    all: Joi.object({
      enabled: Joi.boolean().required(),
      readOnly: Joi.boolean().required()
    }).required(),
    channels: Joi.object().pattern(
      Joi.string(),
      Joi.object({
        enabled: Joi.boolean().required()
      })
    ).optional()
  }).optional(),
  workflow: Joi.object({
    all: Joi.object({
      enabled: Joi.boolean().required(),
      readOnly: Joi.boolean().required()
    }).required(),
    channels: Joi.object().pattern(
      Joi.string(),
      Joi.object({
        enabled: Joi.boolean().required()
      })
    ).optional()
  }).optional()
});

// List workflows validation
const listWorkflows = {
  query: Joi.object().keys({
    limit: Joi.number().integer().min(1).max(1000).optional(),
    offset: Joi.number().integer().min(0).optional(),
    orderDirection: Joi.string().valid('ASC', 'DESC').optional(),
    orderBy: Joi.string().valid('createdAt', 'updatedAt', 'name', 'lastTriggeredAt').optional(),
    query: Joi.string().optional(),
    tags: Joi.alternatives().try(
      Joi.string(),
      Joi.array().items(Joi.string())
    ).optional(),
    status: Joi.alternatives().try(
      Joi.string().valid('ACTIVE', 'INACTIVE', 'ERROR'),
      Joi.array().items(Joi.string().valid('ACTIVE', 'INACTIVE', 'ERROR'))
    ).optional()
  })
};

// Get workflow validation
const getWorkflow = {
  params: Joi.object().keys({
    workflowId: Joi.string().required()
  })
};

// Create workflow validation
const createWorkflow = {
  body: Joi.object().keys({
    name: Joi.string().required(),
    description: Joi.string().optional(),
    tags: Joi.array().items(Joi.string()).optional(),
    active: Joi.boolean().optional().default(false),
    validatePayload: Joi.boolean().optional(),
    payloadSchema: Joi.object().optional(),
    isTranslationEnabled: Joi.boolean().optional().default(false),
    workflowId: Joi.string().required(),
    steps: Joi.array().items(workflowStepSchema).required(),
    __source: Joi.string().valid(
      'template_store', 'editor', 'notification_directory', 'onboarding_digest_demo',
      'onboarding_in_app', 'empty_state', 'dropdown', 'onboarding_get_started',
      'bridge', 'dashboard'
    ).optional().default('editor'),
    preferences: workflowPreferencesSchema.optional()
  })
};

// Update workflow validation
const updateWorkflow = {
  params: Joi.object().keys({
    workflowId: Joi.string().required()
  }),
  body: Joi.object().keys({
    name: Joi.string().optional(),
    description: Joi.string().optional(),
    tags: Joi.array().items(Joi.string()).optional(),
    active: Joi.boolean().optional(),
    validatePayload: Joi.boolean().optional(),
    payloadSchema: Joi.object().optional(),
    isTranslationEnabled: Joi.boolean().optional(),
    steps: Joi.array().items(workflowStepSchema).optional(),
    preferences: workflowPreferencesSchema.optional()
  })
};

// Delete workflow validation
const deleteWorkflow = {
  params: Joi.object().keys({
    workflowId: Joi.string().required()
  })
};

// Sync workflow validation
const syncWorkflow = {
  params: Joi.object().keys({
    workflowId: Joi.string().required()
  }),
  body: Joi.object().optional()
};

// Export workflows validation
const exportWorkflows = {
  body: Joi.object().keys({
    workflowIds: Joi.array().items(Joi.string()).optional()
  })
};

// Bulk export workflows validation
const bulkExportWorkflows = {
  query: Joi.object().keys({
    workflowIds: Joi.alternatives().try(
      Joi.string(),
      Joi.array().items(Joi.string())
    ).required()
  })
};

// Import workflows validation
const importWorkflows = {
  body: Joi.object().keys({
    importData: Joi.object({
      exportedAt: Joi.string().isoDate().required(),
      version: Joi.string().required(),
      totalWorkflows: Joi.number().integer().min(0).required(),
      workflows: Joi.array().items(
        Joi.object({
          name: Joi.string().required(),
          description: Joi.string().optional(),
          tags: Joi.array().items(Joi.string()).optional(),
          active: Joi.boolean().optional(),
          validatePayload: Joi.boolean().optional(),
          payloadSchema: Joi.object().optional(),
          isTranslationEnabled: Joi.boolean().optional(),
          workflowId: Joi.string().required(),
          steps: Joi.array().items(workflowStepSchema).required(),
          preferences: workflowPreferencesSchema.optional(),
          originalId: Joi.string().optional(),
          originalWorkflowId: Joi.string().optional()
        })
      ).required()
    }).required(),
    options: Joi.object({
      overwriteExisting: Joi.boolean().optional().default(false),
      skipExisting: Joi.boolean().optional().default(true),
      updateWorkflowIds: Joi.boolean().optional().default(true)
    }).optional()
  })
};

// Import workflows from platform validation
const importWorkflowsFromPlatform = {
  body: Joi.object().keys({
    fetchOptions: Joi.object({
      limit: Joi.number().integer().min(1).max(1000).optional().default(100),
      offset: Joi.number().integer().min(0).optional().default(0),
      orderDirection: Joi.string().valid('ASC', 'DESC').optional().default('DESC'),
      orderBy: Joi.string().valid('createdAt', 'updatedAt', 'name', 'lastTriggeredAt').optional().default('createdAt'),
      query: Joi.string().optional().allow('').default(''),
      tags: Joi.alternatives().try(
        Joi.string().allow(''),
        Joi.array().items(Joi.string())
      ).optional().default([]).custom((value) => {
        if (typeof value === 'string') {
          return value ? [value] : [];
        }
        return value || [];
      }),
      status: Joi.alternatives().try(
        Joi.string().valid('ACTIVE', 'INACTIVE', 'ERROR').allow(''),
        Joi.array().items(Joi.string().valid('ACTIVE', 'INACTIVE', 'ERROR'))
      ).optional().default([]).custom((value) => {
        if (typeof value === 'string') {
          return value ? [value] : [];
        }
        return value || [];
      })
    }).optional().default({}),
    importOptions: Joi.object({
      overwriteExisting: Joi.boolean().optional().default(false),
      skipExisting: Joi.boolean().optional().default(true),
      updateWorkflowIds: Joi.boolean().optional().default(true)
    }).optional().default({})
  })
};

// Fetch workflows from platform validation
const fetchWorkflowsFromPlatform = {
  query: Joi.object().keys({
    limit: Joi.number().integer().min(1).max(1000).optional(),
    offset: Joi.number().integer().min(0).optional(),
    orderDirection: Joi.string().valid('ASC', 'DESC').optional(),
    orderBy: Joi.string().valid('createdAt', 'updatedAt', 'name', 'lastTriggeredAt').optional(),
    query: Joi.string().optional().allow(''),
    tags: Joi.alternatives().try(
      Joi.string().allow(''),
      Joi.array().items(Joi.string())
    ).optional().custom((value) => {
      // Convert single string to array for consistency
      if (typeof value === 'string') {
        return value ? [value] : [];
      }
      return value || [];
    }),
    status: Joi.alternatives().try(
      Joi.string().valid('ACTIVE', 'INACTIVE', 'ERROR').allow(''),
      Joi.array().items(Joi.string().valid('ACTIVE', 'INACTIVE', 'ERROR'))
    ).optional().custom((value) => {
      // Convert single string to array for consistency
      if (typeof value === 'string') {
        return value ? [value] : [];
      }
      return value || [];
    })
  })
};

// Export to Novu validation
const exportToNovu = {
  body: Joi.object().keys({
    workflowIds: Joi.array().items(Joi.string()).optional().default([])
  })
};

module.exports = {
  listWorkflows,
  getWorkflow,
  createWorkflow,
  updateWorkflow,
  deleteWorkflow,
  syncWorkflow,
  exportWorkflows,
  bulkExportWorkflows,
  importWorkflows,
  importWorkflowsFromPlatform,
  fetchWorkflowsFromPlatform,
  exportToNovu
};
