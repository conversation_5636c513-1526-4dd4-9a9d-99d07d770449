const express = require("express");
const validate = require("../middlewares/validate");
const auth = require("../middlewares/auth");
const { MystaffValidation } = require("../validations");
const { MystaffController } = require("../controllers");

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: MyStaff
 *   description: Staff management for managers - view direct reports and hierarchical reporting structure
 */

/**
 * @swagger
 * /my-staff/direct-reports:
 *   get:
 *     summary: Get people who report directly to the logged-in user
 *     tags: [MyStaff]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         required: false
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         required: false
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of items per page
 *       - in: query
 *         name: search
 *         required: false
 *         schema:
 *           type: string
 *         description: Search by name or email
 *     responses:
 *       200:
 *         description: Direct reports retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Direct reports retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     totalItems:
 *                       type: integer
 *                       example: 15
 *                     totalPages:
 *                       type: integer
 *                       example: 2
 *                     currentPage:
 *                       type: integer
 *                       example: 1
 *                     data:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           identity_id:
 *                             type: string
 *                             format: uuid
 *                             example: "456e7890-e89b-12d3-a456-************"
 *                           first_name:
 *                             type: string
 *                             example: "John"
 *                           last_name:
 *                             type: string
 *                             example: "Doe"
 *                           email:
 *                             type: string
 *                             example: "<EMAIL>"
 *                           eid:
 *                             type: string
 *                             example: "EMP001"
 *                           job_title:
 *                             type: string
 *                             example: "Software Engineer"
 *                           company:
 *                             type: string
 *                             example: "Tech Corp"
 *                           start_date:
 *                             type: string
 *                             format: date
 *                             example: "2024-01-15"
 *                           status:
 *                             type: string
 *                             example: "Active"
 *       404:
 *         description: No direct reports found
 *       401:
 *         description: Unauthorized
 */
router.get(
  "/direct-reports",
  auth("view_my_access"),
  validate(MystaffValidation.getDirectReports),
  MystaffController.getDirectReports
);

/**
 * @swagger
 * /my-staff/my-organization:
 *   get:
 *     summary: Get people who report to the logged-in user and their subordinates (hierarchical reporting structure)
 *     tags: [MyStaff]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         required: false
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         required: false
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of items per page
 *       - in: query
 *         name: search
 *         required: false
 *         schema:
 *           type: string
 *         description: Search by name or email
 *       - in: query
 *         name: level
 *         required: false
 *         schema:
 *           type: integer
 *           default: 2
 *         description: Maximum hierarchy levels to include (1=direct reports only, 2=direct + their reports, etc.)
 *     responses:
 *       200:
 *         description: Hierarchical reports retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Hierarchical reports retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     totalItems:
 *                       type: integer
 *                       example: 25
 *                     totalPages:
 *                       type: integer
 *                       example: 3
 *                     currentPage:
 *                       type: integer
 *                       example: 1
 *                     data:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           identity_id:
 *                             type: string
 *                             format: uuid
 *                             example: "456e7890-e89b-12d3-a456-************"
 *                           first_name:
 *                             type: string
 *                             example: "John"
 *                           last_name:
 *                             type: string
 *                             example: "Doe"
 *                           email:
 *                             type: string
 *                             example: "<EMAIL>"
 *                           eid:
 *                             type: string
 *                             example: "EMP001"
 *                           job_title:
 *                             type: string
 *                             example: "Software Engineer"
 *                           company:
 *                             type: string
 *                             example: "Tech Corp"
 *                           start_date:
 *                             type: string
 *                             format: date
 *                             example: "2024-01-15"
 *                           status:
 *                             type: string
 *                             example: "Active"
 *                           reporting_level:
 *                             type: integer
 *                             example: 1
 *                             description: "1 = direct report, 2 = reports to direct report, etc."
 *                           manager_name:
 *                             type: string
 *                             example: "Jane Smith"
 *                             description: "Name of the immediate manager"
 *       404:
 *         description: No hierarchical reports found
 *       401:
 *         description: Unauthorized
 */
router.get(
  "/my-organization",
  auth("view_my_access"),
  validate(MystaffValidation.getHierarchicalReports),
  MystaffController.getHierarchicalReports
);

module.exports = router;
