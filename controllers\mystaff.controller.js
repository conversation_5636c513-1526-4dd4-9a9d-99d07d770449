const { 
  Identity,
  MasterData
} = require("../models");
const httpStatus = require("http-status");
const { sendSuccess, sendError, catchAsync } = require("../helpers/api.helper");
const { paginate } = require("../models/plugins/paginate.plugin");
const { Op } = require("sequelize");

/**
 * @desc Get people who report directly to the logged-in user
 * @param {Object} req - Express request object
 * @param {Object} req.identity - Contains the current user's identity_id from JWT token
 * @param {Object} req.query - Query parameters for filtering and pagination
 * @param {Object} res - Express response object
 * @returns {JSON} JSON object with direct reports information
 */
exports.getDirectReports = catchAsync(async (req, res) => {
  const { identity_id } = req.identity;
  const { page, limit, search } = req.query;

  if (!identity_id) {
    return sendError(res, "User identity not found in token", httpStatus.UNAUTHORIZED);
  }

  // Build where conditions for filtering
  const whereConditions = {
    manager: identity_id,
    status: { [Op.ne]: null } // Exclude null status (assuming null means inactive)
  };

  // Add search functionality
  if (search && search.trim()) {
    whereConditions[Op.or] = [
      { first_name: { [Op.iLike]: `%${search.trim()}%` } },
      { last_name: { [Op.iLike]: `%${search.trim()}%` } },
      { email: { [Op.iLike]: `%${search.trim()}%` } },
      { eid: { [Op.iLike]: `%${search.trim()}%` } }
    ];
  }

  // Pagination options
  const paginationOptions = { page, limit };

  // Query options for finding direct reports
  const queryOptions = {
    where: whereConditions,
    attributes: [
      'identity_id',
      'first_name',
      'last_name',
      'email',
      'eid',
      'job_title',
      'company',
      'organization',
      'start_date',
      'status'
    ],
    include: [
      {
        model: MasterData,
        as: 'identity_status_name',
        attributes: ['value']
      }
    ],
    order: [['first_name', 'ASC'], ['last_name', 'ASC']]
  };

  const result = await paginate(Identity, queryOptions, paginationOptions);

  if (!result.data || result.data.length === 0) {
    return sendSuccess(res, "No direct reports found", httpStatus.OK, {
      totalItems: 0,
      totalPages: 0,
      currentPage: parseInt(page) || 1,
      data: []
    });
  }

  // Format the response data
  const formattedData = {
    ...result,
    data: result.data.map(person => ({
      identity_id: person.identity_id,
      first_name: person.first_name,
      last_name: person.last_name,
      email: person.email,
      eid: person.eid,
      job_title: person.job_title,
      company: person.company,
      organization: person.organization,
      start_date: person.start_date,
      status: person.identity_status_name?.value || 'Unknown'
    }))
  };

  sendSuccess(res, "Direct reports retrieved successfully", httpStatus.OK, formattedData);
});

/**
 * @desc Get people who report to the logged-in user and their subordinates (hierarchical reporting structure)
 * @param {Object} req - Express request object
 * @param {Object} req.identity - Contains the current user's identity_id from JWT token
 * @param {Object} req.query - Query parameters for filtering and pagination
 * @param {Object} res - Express response object
 * @returns {JSON} JSON object with hierarchical reports information
 */
exports.getHierarchicalReports = catchAsync(async (req, res) => {
  const { identity_id } = req.identity;
  const { page, limit, search, level = 2 } = req.query;

  if (!identity_id) {
    return sendError(res, "User identity not found in token", httpStatus.UNAUTHORIZED);
  }

  /**
   * Recursive function to get all subordinates up to a specified level
   * @param {string} managerId - The manager's identity_id
   * @param {number} currentLevel - Current hierarchy level (1 = direct reports)
   * @param {number} maxLevel - Maximum level to traverse
   * @returns {Promise<Array>} Promise that resolves to array of subordinate identity_ids with their levels
   */
  const getSubordinatesRecursively = async (managerId, currentLevel, maxLevel) => {
    if (currentLevel > maxLevel) {
      return [];
    }

    // Get direct reports for this manager
    const directReports = await Identity.findAll({
      where: {
        manager: managerId,
        status: { [Op.ne]: null }
      },
      attributes: ['identity_id', 'first_name', 'last_name']
    });

    let allSubordinates = [];

    // Add direct reports with their level
    for (const report of directReports) {
      allSubordinates.push({
        identity_id: report.identity_id,
        reporting_level: currentLevel,
        manager_id: managerId
      });

      // Recursively get their subordinates if we haven't reached max level
      if (currentLevel < maxLevel) {
        const subSubordinates = await getSubordinatesRecursively(
          report.identity_id,
          currentLevel + 1,
          maxLevel
        );
        allSubordinates = allSubordinates.concat(subSubordinates);
      }
    }

    return allSubordinates;
  };

  // Get all subordinates up to the specified level
  const allSubordinates = await getSubordinatesRecursively(identity_id, 1, level);

  if (!allSubordinates || allSubordinates.length === 0) {
    return sendSuccess(res, "No hierarchical reports found", httpStatus.OK, {
      totalItems: 0,
      totalPages: 0,
      currentPage: parseInt(page) || 1,
      data: []
    });
  }

  // Extract identity_ids for the main query
  const subordinateIds = allSubordinates.map(sub => sub.identity_id);

  // Build where conditions for filtering
  const whereConditions = {
    identity_id: { [Op.in]: subordinateIds },
    status: { [Op.ne]: null }
  };

  // Add search functionality
  if (search && search.trim()) {
    whereConditions[Op.and] = [
      { identity_id: { [Op.in]: subordinateIds } },
      {
        [Op.or]: [
          { first_name: { [Op.iLike]: `%${search.trim()}%` } },
          { last_name: { [Op.iLike]: `%${search.trim()}%` } },
          { email: { [Op.iLike]: `%${search.trim()}%` } },
          { eid: { [Op.iLike]: `%${search.trim()}%` } }
        ]
      }
    ];
    delete whereConditions.identity_id; // Remove the original condition since it's now in [Op.and]
  }

  // Pagination options
  const paginationOptions = { page, limit };

  // Query options for finding hierarchical reports
  const queryOptions = {
    where: whereConditions,
    attributes: [
      'identity_id',
      'first_name',
      'last_name',
      'email',
      'eid',
      'job_title',
      'company',
      'organization',
      'start_date',
      'status',
      'manager'
    ],
    include: [
      {
        model: MasterData,
        as: 'identity_status_name',
        attributes: ['value']
      },
      {
        model: Identity,
        as: 'manager_identity',
        attributes: ['first_name', 'last_name', 'email'],
        required: false
      }
    ],
    order: [['first_name', 'ASC'], ['last_name', 'ASC']]
  };

  const result = await paginate(Identity, queryOptions, paginationOptions);

  if (!result.data || result.data.length === 0) {
    return sendSuccess(res, "No hierarchical reports found", httpStatus.OK, {
      totalItems: 0,
      totalPages: 0,
      currentPage: parseInt(page) || 1,
      data: []
    });
  }

  // Create a map for quick lookup of reporting levels
  const levelMap = {};
  allSubordinates.forEach(sub => {
    levelMap[sub.identity_id] = sub.reporting_level;
  });

  // Format the response data
  const formattedData = {
    ...result,
    data: result.data.map(person => ({
      identity_id: person.identity_id,
      first_name: person.first_name,
      last_name: person.last_name,
      email: person.email,
      eid: person.eid,
      job_title: person.job_title,
      company: person.company,
      organization: person.organization,
      start_date: person.start_date,
      status: person.identity_status_name?.value || 'Unknown',
      reporting_level: levelMap[person.identity_id] || 1,
      manager_name: person.manager_identity ?
        `${person.manager_identity.first_name} ${person.manager_identity.last_name}` :
        'Unknown'
    }))
  };

  sendSuccess(res, "Hierarchical reports retrieved successfully", httpStatus.OK, formattedData);
});
