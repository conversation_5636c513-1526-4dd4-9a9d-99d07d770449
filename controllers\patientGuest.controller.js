const {
  Pat<PERSON>Guest,
  AppointmentGuest,
  sequelize,
  Appointment,
  Patient,
  PatientAppointmentGuestView,
  AppointmentGuest<PERSON><PERSON>ckin ,
  PatientGuestHistoryView,
  AppointmentGuestScreening,
  AppointmentGuestScreeningMatch,
  GuestScreeningView

} = require("../models");
const { sendSuccess, sendError, catchAsync } = require("../helpers/api.helper");
const { status: httpStatus, default: status } = require("http-status");
const { Op, fn, col, QueryTypes, where: sequelizeWhere } = require("sequelize");
const { paginate } = require("../models/plugins/paginate.plugin");
const { generateOTP, transformBooleans } = require("../helpers/global.helper");
const {formatIdentity} = require("../helpers/global.helper");

/**
 * Search today's patient guests (paginated) with four fields:
 *   • appointment_guest_id
 *   • appointment_date
 *   • patient_full_name
 *   • guest_image
 *
 * @query {string} [search] filter on patient name
 * @query {number} [type=0] guest type key
 * @query {number} [page] pagination page
 * @query {number} [limit] pagination limit
 * @returns {Promise<void>}
 */
exports.search = catchAsync(async (req, res) => {
  const { search = "" , facility_id} = req.query;
  const where = {};

  if (search) {
    where[Op.or] = [
      { first_name: { [Op.iLike]: `%${search}%` } },
      { last_name: { [Op.iLike]: `%${search}%` } },
      sequelizeWhere(
        fn('CONCAT', col('first_name'), ' ', col('last_name')),
        { [Op.iLike]: `%${search}%` }
      ),
    ];
  }

   if (facility_id) {
    where.facility_id = facility_id;
  }
  const appointments = await PatientAppointmentGuestView.findAll({
    where,
    attributes: [
      "appointment_guest_id",
      "appointment_date",
      "patient_full_name",
      "guest_image",
      "appointment_id",
      "first_name",
      "last_name",
      "mrn",
      "patient_id",
      "facility_id"
    ],
    order: [["appointment_date", "DESC"]],
  });

  sendSuccess(res, appointments, httpStatus.OK);
});


// Create guest

exports.create = catchAsync(async (req, res) => {
  const transaction = req.transaction; // Get the transaction from the request
  const {
    first_name,
    last_name,
    email,
    phone,
    organization,
    guest_type,
    relationship_type,
    related_person_name,
    related_person_contact,
    relationship_status,
    is_emergency_contact,
    emergency_contact_priority,
    can_make_decisions,
    has_custody,
    lives_with_patient,
    relationship_notes,
    effective_from,
    effective_to,
    reason,
    denied_on,
    appointment_id,
    start_date,
    start_time,
    duration,
    escort_name,
    facility_id,
    screening,
    image,
    is_walkin,
    friends_and_family,
     updated_by,
  } = req.body;

  if (guest_type === 2 && !reason) {
    return sendError(
      res,
      "Reason is required when guest_type is 2",
      httpStatus.BAD_REQUEST
    );
  }

  const appointment = await Appointment.findOne({
    where: { appointment_id },
    attributes: ["patient_id"],
  });

  if (!appointment) {
    return sendError(res, "Appointment not found", httpStatus.NOT_FOUND);
  }

  const { patient_id } = appointment;
  const guest_pin = generateOTP(6);

  // const adjustedFriendsAndFamily = relationship_type === 2 ? true : friends_and_family;
  const adjustedIsWalkin = relationship_type === 2 ? false : is_walkin;

  const patientGuest = await PatientGuest.create(
    {
      first_name,
      last_name,
      email,
      phone,
      organization,
      guest_type,
      relationship_type,
      related_person_name,
      related_person_contact,
      relationship_status,
      is_emergency_contact,
      emergency_contact_priority,
      can_make_decisions,
      has_custody,
      lives_with_patient,
      relationship_notes,
      effective_from,
      effective_to,
      reason,
      denied_on,
      patient_id,
      image,
      is_walkin: adjustedIsWalkin,
      friends_and_family, 
      updated_by,

    },
    { transaction }
  );

  const appointmentGuest = await AppointmentGuest.create(
    {
      appointment_id,
      patient_guest_id: patientGuest.patient_guest_id,
      start_date,
      start_time,
      duration,
      escort_name,
      facility_id,
      screening,
      guest_pin,
      updated_by,
    },
    { transaction }
  );

  sendSuccess(
    res,
    {
      message: "Guest added successfully",
      patientGuest,
      appointmentGuest,
    },
    httpStatus.CREATED
  );
});

// Create denied guest
exports.addGuest = catchAsync(async (req, res) => {
  const transaction = req.transaction; // Get the transaction from the request  try {
  const {
    first_name,
    last_name,
    email,
    phone,
    guest_type,
    reason,
    denied_on,
    patient_id,
    relationship_type,
    relationship_status,
    image,
    birth_date,
    updated_by,
  } = req.body;

   if (guest_type === 2 && !reason) {
      return sendError(res, "Reason is required for denied guest", httpStatus.BAD_REQUEST);
    }
  const patient = await Patient.findOne({
    where: { patient_id },
  });

  if (!patient) {
    return sendError(res, "Appointment not found", httpStatus.NOT_FOUND);
  }

  const patientGuest = await PatientGuest.create(
    {
      first_name,
      last_name,
      email,
      phone,
      guest_type,
      reason,
      denied_on,
      patient_id,
      relationship_type,
      relationship_status,
      image,
      birth_date,
      updated_by
    },
    { transaction }
  );
  sendSuccess(
    res,
    {message:
    "Guest added successfully",
    patientGuest },
    httpStatus.CREATED,
    
  );
});
/* The above code is a JavaScript function that serves as an endpoint handler for fetching guest
information based on certain query parameters. Here is a breakdown of what the code is doing: */

exports.index = catchAsync(async (req, res) => {
  const {
    guest_name,
    guest_type,
    appointment_guest_status,
    appointment_id,
    page = 1,
    limit = 10,
    sortBy = "guest_arrival_time",
    sortOrder = "DESC",
  } = req.query;

  if (!appointment_id) {
    return sendError(res, "appointment_id is required", httpStatus.BAD_REQUEST);
  }

  // Shared filters
  const nameCondition = guest_name
    ? {
        [Op.or]: [
          { first_name: { [Op.iLike]: `%${guest_name}%` } },
          { last_name: { [Op.iLike]: `%${guest_name}%` } },
        ],
      }
    : {};

  const typeCondition = guest_type ? { guest_type } : {};
  const statusCondition = appointment_guest_status
    ? { appointment_guest_status }
    : {};

  // Guests for the given appointment_id
  const appointmentConditions = {
    appointment_id,
    ...nameCondition,
    ...typeCondition,
    ...statusCondition,
  };

  const queryOptions = {
    where: appointmentConditions,
    attributes: [
      "patient_guest_id",
      "patient_id",
      "first_name",
      "last_name",
      "guest_image",
      "screening",
      "mrn",
      "guest_arrival_time",
      "guest_departure_time",
      "appointment_guest_id",
      "guest_pin",
      "appointment_guest_status",
      "appointment_guest_status_name",
      "friends_and_family",
      "is_walkin",
    ],
    order: [[sortBy, sortOrder.toUpperCase() === "ASC" ? "ASC" : "DESC"]],
  };

  const result = await paginate(PatientAppointmentGuestView, queryOptions, {
    page,
    limit,
    sortBy: sortBy || "guest_arrival_time",
    sortOrder: sortOrder || "DESC",
  });

  if (result && Array.isArray(result.data)) {
    result.data = result.data.map(item => {
      const plain = item.toJSON(); // Convert Sequelize instance to plain object
      return transformBooleans(plain, PatientAppointmentGuestView); // Pass model for accurate boolean detection
    });
  }
  sendSuccess(
    res,
    "Guest view data fetched successfully",
    httpStatus.OK,
    result
  );
});

// Override Screening
exports.overrideScreening = catchAsync(async (req, res) => {
  const { appointment_guest_screening_id } = req.params;
  const { reason } = req.body;  
  const loggedInUser = req.identity;

  // Fetch screening record
  const screening = await AppointmentGuestScreening.findOne({
    where: { appointment_guest_screening_id },
  });

  if (!screening) {
    return sendError(res, "Screening record not found", httpStatus.NOT_FOUND);
  }

  // Fetch the associated appointment_guest record
  const appointmentGuest = await AppointmentGuest.findByPk(screening.appointment_guest_id);
  if (!appointmentGuest) {
    return sendError(res, "Appointment guest not found", httpStatus.NOT_FOUND);
  }

  // Update the appointment_guest record
  appointmentGuest.status = 0;
  appointmentGuest.screening = 0;
  await appointmentGuest.save();

  // Update override fields in screening record
  await screening.update({
    override:0,
    override_by: loggedInUser.identity_id,
    override_time: new Date(),
    reason,
  });

   const responseData = {
    appointmentGuest: appointmentGuest,
    screening: {
      override: 0,
      override_by: loggedInUser.identity_id,
      override_time: new Date(),
      reason,
    },
  };
  return sendSuccess(res, "Screening override successful", httpStatus.OK , responseData);
});


// Get Screening matches
exports.getScreeningMatches = catchAsync(async (req, res) => {
  const { appointment_guest_screening_id } = req.params;
  const screeningMatches = await GuestScreeningView.findAll({
    where: {
      appointment_guest_screening_id,
    },
    order: [['appointment_guest_screening_id', 'DESC']],  // Get the latest screening match
  });
  if (screeningMatches.length === 0) {
    return sendError(res, "Guest not found or no screening matches found", httpStatus.NOT_FOUND);
  }
  const formattedResult = await Promise.all(
    screeningMatches.map(async (record) => {
      const IdentityId = record.added_by; // Change here to use added_by
      const formattedIdentity = await formatIdentity(IdentityId);
      return {
        ...record.get({ plain: true }),
        added_by: formattedIdentity, // Change here to use added_by
      };
    })
  );
  return sendSuccess(res, "Screening matches retrieved successfully", httpStatus.OK, {
    data: formattedResult, // Return the formatted result
  });
});


// Check in check out for guest

exports.checkGuest = catchAsync(async (req, res) => {
  const { appointment_guest_id } = req.params;
  const { action, appointment_id } = req.query;

  const appointmentGuest = await AppointmentGuest.findByPk(appointment_guest_id, {
    include: [{ model: PatientGuest, as: 'patientGuest' }],
  });

  if (!appointmentGuest) {
    return sendError(res, "Guest not found", httpStatus.NOT_FOUND);
  }
  const guestFullName = `${appointmentGuest.patientGuest.first_name} ${appointmentGuest.patientGuest.last_name}`;
  const metaphoneLength = 7;

  if (action === "checkIn") {
    // Check if guest already failed screening but override is done
    const existingScreening = await AppointmentGuestScreening.findOne({
      where: { appointment_guest_id },
      order: [['createdAt', 'DESC']],
    });

   if (existingScreening) {
  if (existingScreening.override === 0 && appointmentGuest.screening === 0) {
    return await performCheckIn(req, res, appointmentGuest, appointment_id);
  } else if (appointmentGuest.screening === 2) {
    return sendError(res, "Guest override revoked by patient. Access denied.", httpStatus.FORBIDDEN);
  }
}
    // Run screening
    const [deniedGuests, watchlistMatches] = await Promise.all([
      sequelize.query(`
        SELECT patient_guest_id AS matched_id, first_name || ' ' || last_name AS full_name, 2 as guest_type
        FROM patient_guest
        WHERE guest_type = 2
        AND (
          soundex(first_name || ' ' || last_name) = soundex(:name)
          OR metaphone(first_name || ' ' || last_name, :len) = metaphone(:name, :len)
        )
      `, {
        replacements: { name: guestFullName, len: metaphoneLength },
        type: QueryTypes.SELECT,
      }),

      sequelize.query(`
        SELECT watchlist_id AS matched_id, first_name || ' ' || last_name AS full_name
        FROM watchlist
        WHERE (
          soundex(first_name || ' ' || last_name) = soundex(:name)
          OR metaphone(first_name || ' ' || last_name, :len) = metaphone(:name, :len)
        )
      `, {
        replacements: { name: guestFullName, len: metaphoneLength },
        type: QueryTypes.SELECT,
      }),
    ]);

    const allMatches = [...deniedGuests, ...watchlistMatches];

    if (allMatches.length > 0) {
      appointmentGuest.screening = 1;
      appointmentGuest.status = 0;
      await appointmentGuest.save();

      const screening = await AppointmentGuestScreening.create({
        appointment_guest_id,
      });

      for (const match of allMatches) {
        await AppointmentGuestScreeningMatch.create({
          appointment_guest_screening_id: screening.appointment_guest_screening_id,
          match_id: match.matched_id,
          match_name: match.full_name,
          type: match.guest_type === 2 ? 0 : 1,
        });
      }
      return sendSuccess(res, "Guest failed screening. Approval required.", httpStatus.OK, {
        appointment_guest_id,
        appointment_guest_screening_id: screening.appointment_guest_screening_id,
        screening:1,
        matches_found: allMatches.length,
      });
    }
    return await performCheckIn(req, res, appointmentGuest, appointment_id);
  }

  //  Check-Out
  if (action === "checkOut") {
    appointmentGuest.status = 2;
    appointmentGuest.departure_time = new Date();
    await appointmentGuest.save();

    const checkinRecord = await AppointmentGuestCheckin.findOne({
      where: { appointment_guest_id },
      order: [["createdAt", "DESC"]],
    });

    if (checkinRecord) {
      checkinRecord.checkout_time = new Date();
      await checkinRecord.save();
    }

    return sendSuccess(res, "Guest checked out successfully", httpStatus.OK, {
      appointment_guest_id,
      guest_status: "Checked-Out",
    });
  }

  return sendError(res, "Invalid action. Use 'checkIn' or 'checkOut'.", httpStatus.BAD_REQUEST);
});

const performCheckIn = async (req, res, appointmentGuest, appointment_id) => {
  if (appointment_id && appointmentGuest.appointment_id !== appointment_id) {
    const newAppointmentGuest = await AppointmentGuest.create({
      appointment_id,
      patient_guest_id: appointmentGuest.patient_guest_id,
      status: 1,
      arrival_time: new Date(),
      guest_pin: generateOTP(6),
    });

    await AppointmentGuestCheckin.create({
      appointment_guest_id: newAppointmentGuest.appointment_guest_id,
      checkin_time: new Date(),
    });

    return sendSuccess(res, "Guest checked in for current appointment", httpStatus.OK, {
      appointment_guest_id: newAppointmentGuest.appointment_guest_id,
      guest_status: "Checked-In",
    });
  } else {
    appointmentGuest.status = 1;
    appointmentGuest.arrival_time = new Date();
    await appointmentGuest.save();

    await AppointmentGuestCheckin.create({
      appointment_guest_id: appointmentGuest.appointment_guest_id,
      checkin_time: new Date(),
    });

    return sendSuccess(res, "Guest checked in successfully", httpStatus.OK, {
      appointment_guest_id: appointmentGuest.appointment_guest_id,
      guest_status: "Checked-In",
    });
  }
};




/**
 * Update the image for a patient guest.
 *
 * @param {string} patient_guest_id
 * @body {string} image
 * @returns {Promise<void>}
 */
exports.image = catchAsync(async (req, res) => {
  const { patient_guest_id } = req.params;
  const patientGuest = await PatientGuest.findByPk(patient_guest_id);
  const { image } = req.body; // or req.file if using multer
  patientGuest.image = image;
  await patientGuest.save();
  return sendSuccess(
    res,
    "Guest image updated successfully",
    httpStatus.OK,
    patientGuest
  );
});

// GuestList of Patient Hub
exports.guestList = catchAsync(async (req, res) => {
  const {
    patient_id,
    search = "",
    appointment_status,
    guest_arrival_time,
    guest_departure_time,
    page = 1,
    limit = 10,
    sortBy = 'guest_arrival_time',
    sortOrder = 'DESC'
  } = req.query;
  if (!patient_id) {
    return sendError(res, "patient_id is required", httpStatus.BAD_REQUEST);
  }

  const guestWhere = {
    patient_id,
  };
  if (appointment_status !== undefined) {
    guestWhere.appointment_guest_status = appointment_status;
  }
  if (search) {
    guestWhere.guest_full_name = { [Op.iLike]: `%${search}%` };
  }
  if (guest_arrival_time) {
    guestWhere.guest_arrival_time = {
      [Op.gte]: new Date(guest_arrival_time),
    };
  }
  if (guest_departure_time) {
    guestWhere.guest_departure_time = {
      [Op.lte]: new Date(guest_departure_time),
    };
  }
  const queryOptions = {
    where: guestWhere,
    attributes: [
      "guest_full_name",
      "guest_arrival_time",
      "guest_departure_time",
      "facility_name",
      "building_name",
      "floor_number",
      "room_number",
      "appointment_guest_status",
      "appointment_guest_status_name",
    ],
    order: [[sortBy, sortOrder.toUpperCase() === 'ASC' ? 'ASC' : 'DESC']],
  };
  const result = await paginate(PatientAppointmentGuestView, queryOptions, { page, limit, sortBy,
    sortOrder });
  sendSuccess(res, "Guests retrieved successfully for the patient", httpStatus.OK, result);
});


exports.friends = catchAsync(async (req, res) => {
  const { patient_id, search = "", guest_type, page = 1, limit = 10, sortBy = 'first_name', sortOrder = 'ASC' } = req.query;
  if (!patient_id) {
    return sendError(res, "patient_id is required", httpStatus.BAD_REQUEST);
  }
  const where = {
    ...(patient_id && { patient_id }),
  };
  if (search) {
    where[Op.or] = [
      { first_name: { [Op.iLike]: `%${search}%` } },
      { last_name: { [Op.iLike]: `%${search}%` } },
    ];
  }
  if (guest_type) {
    where.guest_type = 1; // Assuming 1 corresponds to friends
  }
  const queryOptions = {
    where,
    attributes: [
      "first_name",
      "last_name",
      "email",
      "phone",
      "relationship_type",
      "patient_id",
      "patient_guest_id"
    ],
    order: [[sortBy, sortOrder.toUpperCase() === 'ASC' ? 'ASC' : 'DESC']],
  };

  const result = await paginate(PatientGuest, queryOptions,  { page, limit, sortBy, sortOrder });
  sendSuccess(res, "Guests retrieved successfully for the patient", httpStatus.OK, result);
});

exports.denied = catchAsync(async (req, res) => {
  const { patient_id, search = "", guest_type, page = 1, limit = 10, sortBy = 'first_name', sortOrder = 'ASC' } = req.query;
  const where = {
    ...(patient_id && { patient_id }),
  };
  if (search) {
    where[Op.or] = [
      { first_name: { [Op.iLike]: `%${search}%` } },
      { last_name: { [Op.iLike]: `%${search}%` } },
    ];
  }
  if (guest_type) {
    where.guest_type = 2;
  }
  const queryOptions = {
    where,
    attributes: [
      "first_name",
      "last_name", // Concatenate first_name and last_name
      "email",
      "phone",
      "reason",
      "denied_on",
      [fn('DATE', col('birth_date')), 'birth_date'],
      "patient_id",
      "patient_guest_id"
    ],
    order: [[sortBy, sortOrder.toUpperCase() === 'ASC' ? 'ASC' : 'DESC']],
  };
  const result = await paginate(PatientGuest, queryOptions, { page, limit, sortBy, sortOrder });
  sendSuccess(res, "Guests retrieved successfully for the patient", httpStatus.OK, result);
});

/**
 * Update a guest's details.
 *
 * @async
 * @function updateGuest
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends a success response with the updated guest details.
 */
exports.updateGuest = catchAsync(async (req, res) => {
  const { patient_guest_id } = req.params;
  const {
    first_name,
    last_name,
    email,
    phone,
    guest_type,
    relationship_type,
    appointment_id,
    reason,
    denied_on,
  } = req.body;

  const patientGuest = await PatientGuest.findByPk(patient_guest_id);
  if (!patientGuest) {
    return sendError(res, "Patient guest not found", httpStatus.NOT_FOUND);
  }

  await patientGuest.update({
    first_name,
    last_name,
    email,
    phone,
    guest_type,
    relationship_type,
    appointment_id,
    reason,
    denied_on,
  });

  sendSuccess(res, "Guest updated successfully", httpStatus.OK, patientGuest);
});

/**
 * Update a friend (guest_type = 1) in PatientGuest.
 *
 * @async
 * @function updateFriend
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends a success response with the updated friend details.
 */
exports.updateFriend = catchAsync(async (req, res) => {
  const { patient_guest_id } = req.params;
  const {
    first_name,
    last_name,
    email,
    phone,
    relationship_type,
    // Add other fields as needed
  } = req.body;

  const patientGuest = await PatientGuest.findByPk(patient_guest_id);
  if (!patientGuest) {
    return sendError(res, "Friend not found", httpStatus.NOT_FOUND);
  }
  if (patientGuest.guest_type !== 1) {
    return sendError(res, "Not a friend record", httpStatus.BAD_REQUEST);
  }

  await patientGuest.update({
    first_name,
    last_name,
    email,
    phone,
    relationship_type,
    // Add other fields as needed
  });

  sendSuccess(res, "Friend updated successfully", httpStatus.OK, patientGuest);
});

/**
 * Delete a guest by patient_guest_id.
 *
 * @async
 * @function deleteGuest
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends a success response confirming the deletion.
 */
exports.deleteGuest = catchAsync(async (req, res) => {
  const { patient_guest_id } = req.params;

  const patientGuest = await PatientGuest.findByPk(patient_guest_id);
  if (!patientGuest) {
    return sendError(res, "Patient guest not found", httpStatus.NOT_FOUND);
  }

  await patientGuest.destroy();
  sendSuccess(res, "Guest deleted successfully", httpStatus.NO_CONTENT);
});


exports.patientGuestHistory = catchAsync(async (req, res, next) => {
  const { patient_guest_id } = req.query; 
  if (!patient_guest_id) {
    return sendError(res, 'patient_guest_id is required', httpStatus.BAD_REQUEST);
  }
  const results = await PatientGuestHistoryView.findAll({
    where: { patient_guest_id },
  });
  
  if (!results || results.length === 0) {
    return sendError(res, 'No history found for the given patient guest', httpStatus.NOT_FOUND);
  }  

  const formattedResult = await Promise.all(
    results.map(async (record) => {
     const IdentityId = record.modified_by;
      const formattedIdentity = await formatIdentity(IdentityId);
      return {
        ...record.get({ plain: true }),
        modified_by: formattedIdentity,
      };
    })
  );

  sendSuccess(res, 'Patient history retrieved successfully', httpStatus.OK, formattedResult);
});

exports.patientAllGuestHistory = catchAsync(async (req, res, next) => {

  const results = await PatientGuestHistoryView.findAll();
  
  if (!results || results.length === 0) {
    return sendError(res, 'No history found for the given patient guest', httpStatus.NOT_FOUND);
  }  

  const formattedResult = await Promise.all(
    results.map(async (record) => {
     const IdentityId = record.modified_by;
      const formattedIdentity = await formatIdentity(IdentityId);
      return {
        ...record.get({ plain: true }),
        modified_by: formattedIdentity,
      };
    })
  );

  sendSuccess(res, 'Patient history retrieved successfully', httpStatus.OK, formattedResult);
});

exports.revokeOverrideByPatient = catchAsync(async (req, res) => {
  const { appointment_guest_screening_id } = req.params;

  const screening = await AppointmentGuestScreening.findOne({
    where: { appointment_guest_screening_id },
  });

  if (!screening || screening.override !== 0) {
    return sendError(res, "Screening not overridden or not found", httpStatus.BAD_REQUEST);
  }

  const appointmentGuest = await AppointmentGuest.findByPk(screening.appointment_guest_id);

  if (!appointmentGuest) {
    return sendError(res, "Guest not found", httpStatus.NOT_FOUND);
  }

  appointmentGuest.screening = 2; 
  appointmentGuest.status = 0; 
  await appointmentGuest.save();

  return sendSuccess(res, "Override revoked successfully", httpStatus.OK , appointmentGuest);
});
