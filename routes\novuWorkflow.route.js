const express = require('express');
const auth = require('../middlewares/auth');
const validate = require('../middlewares/validate');
const NovuWorkflowController = require('../controllers/novuWorkflow.controller');
const NovuWorkflowValidation = require('../validations/novuWorkflow.validation');

const router = express.Router();

/**
 * @swagger
 * /novu-workflows:
 *   get:
 *     summary: List all Novu workflows
 *     tags: [Novu Workflows]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 1000
 *         description: Number of workflows to return per page
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *           minimum: 0
 *         description: Number of workflows to skip
 *       - in: query
 *         name: orderDirection
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *         description: Sort direction
 *       - in: query
 *         name: orderBy
 *         schema:
 *           type: string
 *           enum: [createdAt, updatedAt, name, lastTriggeredAt]
 *         description: Field to sort by
 *       - in: query
 *         name: query
 *         schema:
 *           type: string
 *         description: Search query to filter workflows
 *       - in: query
 *         name: tags
 *         schema:
 *           type: array
 *           items:
 *             type: string
 *         description: Filter by tags
 *       - in: query
 *         name: status
 *         schema:
 *           type: array
 *           items:
 *             type: string
 *             enum: [ACTIVE, INACTIVE, ERROR]
 *         description: Filter by status
 *     responses:
 *       200:
 *         description: Workflows retrieved successfully
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get(
  '/',
  auth('view_novu_workflows'),
  validate(NovuWorkflowValidation.listWorkflows),
  NovuWorkflowController.listWorkflows
);

/**
 * @swagger
 * /novu-workflows:
 *   post:
 *     summary: Create a new Novu workflow
 *     tags: [Novu Workflows]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - workflowId
 *               - steps
 *             properties:
 *               name:
 *                 type: string
 *                 description: Name of the workflow
 *               description:
 *                 type: string
 *                 description: Description of the workflow
 *               workflowId:
 *                 type: string
 *                 description: Unique identifier for the workflow
 *               tags:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Tags associated with the workflow
 *               active:
 *                 type: boolean
 *                 default: false
 *                 description: Whether the workflow is active
 *               steps:
 *                 type: array
 *                 description: Steps of the workflow
 *                 items:
 *                   type: object
 *                   required:
 *                     - name
 *                     - type
 *                   properties:
 *                     name:
 *                       type: string
 *                     type:
 *                       type: string
 *                       enum: [in_app, email, sms, chat, push, digest, trigger, delay, custom]
 *                     controlValues:
 *                       type: object
 *     responses:
 *       201:
 *         description: Workflow created successfully
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post(
  '/',
  auth('create_novu_workflow'),
  validate(NovuWorkflowValidation.createWorkflow),
  NovuWorkflowController.createWorkflow
);

/**
 * @swagger
 * /novu-workflows/export/template:
 *   get:
 *     summary: Get workflow export template/schema
 *     tags: [Novu Workflows]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Export template retrieved successfully
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get(
  '/export/template',
  auth('view_novu_workflows'),
  NovuWorkflowController.getExportTemplate
);

/**
 * @swagger
 * /novu-workflows/export:
 *   post:
 *     summary: Export Novu workflows to JSON
 *     tags: [Novu Workflows]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               workflowIds:
 *                 type: array
 *                 items:
 *                   type: string
 *     responses:
 *       200:
 *         description: Workflows exported successfully
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post(
  '/export',
  auth('export_novu_workflows'),
  validate(NovuWorkflowValidation.exportWorkflows),
  NovuWorkflowController.exportWorkflows
);

/**
 * @swagger
 * /novu-workflows/import:
 *   post:
 *     summary: Import Novu workflows from JSON data
 *     tags: [Novu Workflows]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - importData
 *     responses:
 *       200:
 *         description: Workflows imported successfully
 *       400:
 *         description: Invalid import data
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post(
  '/import',
  auth('import_novu_workflows'),
  validate(NovuWorkflowValidation.importWorkflows),
  NovuWorkflowController.importWorkflows
);

/**
 * @swagger
 * /novu-workflows/import-from-platform:
 *   post:
 *     summary: Import Novu workflows directly from your Novu platform
 *     tags: [Novu Workflows]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               fetchOptions:
 *                 type: object
 *                 properties:
 *                   limit:
 *                     type: integer
 *                     minimum: 1
 *                     maximum: 1000
 *                     default: 100
 *                     description: Number of workflows to fetch
 *                   offset:
 *                     type: integer
 *                     minimum: 0
 *                     default: 0
 *                     description: Number of workflows to skip
 *                   orderDirection:
 *                     type: string
 *                     enum: [ASC, DESC]
 *                     default: DESC
 *                   orderBy:
 *                     type: string
 *                     enum: [createdAt, updatedAt, name, lastTriggeredAt]
 *                     default: createdAt
 *                   query:
 *                     type: string
 *                     description: Search query to filter workflows
 *                   tags:
 *                     type: array
 *                     items:
 *                       type: string
 *                     description: Filter by workflow tags
 *                   status:
 *                     type: array
 *                     items:
 *                       type: string
 *                       enum: [ACTIVE, INACTIVE, ERROR]
 *                     description: Filter by workflow status
 *               importOptions:
 *                 type: object
 *                 properties:
 *                   overwriteExisting:
 *                     type: boolean
 *                     default: false
 *                     description: Whether to overwrite existing workflows
 *                   skipExisting:
 *                     type: boolean
 *                     default: true
 *                     description: Whether to skip existing workflows
 *                   updateWorkflowIds:
 *                     type: boolean
 *                     default: true
 *                     description: Whether to update workflow IDs to avoid conflicts
 *     responses:
 *       200:
 *         description: Workflows imported from platform successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     imported:
 *                       type: array
 *                       description: Successfully imported workflows
 *                     skipped:
 *                       type: array
 *                       description: Skipped workflows
 *                     errors:
 *                       type: array
 *                       description: Failed imports with error details
 *                     total:
 *                       type: integer
 *                       description: Total workflows processed
 *                     source:
 *                       type: string
 *                       example: platform
 *                     fetchedWorkflows:
 *                       type: integer
 *                       description: Number of workflows fetched from platform
 *       400:
 *         description: Invalid request parameters
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post(
  '/import-from-platform',
  // auth('import_novu_workflows'), // Temporarily disabled for testing
  validate(NovuWorkflowValidation.importWorkflowsFromPlatform),
  NovuWorkflowController.importWorkflowsFromPlatform
);

/**
 * @swagger
 * /novu-workflows/fetch-from-platform:
 *   get:
 *     summary: Fetch workflows from Novu platform without importing
 *     tags: [Novu Workflows]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 1000
 *         description: Number of workflows to fetch
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *           minimum: 0
 *         description: Number of workflows to skip
 *       - in: query
 *         name: orderDirection
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *         description: Sort direction
 *       - in: query
 *         name: orderBy
 *         schema:
 *           type: string
 *           enum: [createdAt, updatedAt, name, lastTriggeredAt]
 *         description: Field to sort by
 *       - in: query
 *         name: query
 *         schema:
 *           type: string
 *         description: Search query to filter workflows
 *       - in: query
 *         name: tags
 *         schema:
 *           type: array
 *           items:
 *             type: string
 *         description: Filter by workflow tags
 *       - in: query
 *         name: status
 *         schema:
 *           type: array
 *           items:
 *             type: string
 *             enum: [ACTIVE, INACTIVE, ERROR]
 *         description: Filter by workflow status
 *     responses:
 *       200:
 *         description: Workflows fetched from platform successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     workflows:
 *                       type: array
 *                       description: Array of workflows from platform
 *                     total:
 *                       type: integer
 *                       description: Total number of workflows fetched
 *       400:
 *         description: Invalid request parameters
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get(
  '/fetch-from-platform',
  auth('view_novu_workflows'),
  validate(NovuWorkflowValidation.fetchWorkflowsFromPlatform),
  NovuWorkflowController.fetchWorkflowsFromPlatform
);

/**
 * @swagger
 * /novu-workflows/imported:
 *   get:
 *     summary: List imported workflows stored in files
 *     tags: [Novu Workflows]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Imported workflows retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     workflows:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           workflowId:
 *                             type: string
 *                           name:
 *                             type: string
 *                           description:
 *                             type: string
 *                           active:
 *                             type: boolean
 *                           tags:
 *                             type: array
 *                             items:
 *                               type: string
 *                           importedAt:
 *                             type: string
 *                             format: date-time
 *                           filePath:
 *                             type: string
 *                           stepsCount:
 *                             type: integer
 *                     total:
 *                       type: integer
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get(
  '/imported',
  auth('view_novu_workflows'),
  NovuWorkflowController.listImportedWorkflows
);

/**
 * @swagger
 * /novu-workflows/imported/{workflowId}:
 *   get:
 *     summary: Get specific imported workflow from file
 *     tags: [Novu Workflows]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: workflowId
 *         required: true
 *         schema:
 *           type: string
 *         description: Workflow ID
 *     responses:
 *       200:
 *         description: Imported workflow retrieved successfully
 *       404:
 *         description: Workflow not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get(
  '/imported/:workflowId',
  auth('view_novu_workflows'),
  NovuWorkflowController.getImportedWorkflow
);

/**
 * @swagger
 * /novu-workflows/imported/{workflowId}:
 *   delete:
 *     summary: Delete imported workflow file
 *     tags: [Novu Workflows]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: workflowId
 *         required: true
 *         schema:
 *           type: string
 *         description: Workflow ID
 *     responses:
 *       200:
 *         description: Imported workflow deleted successfully
 *       404:
 *         description: Workflow not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.delete(
  '/imported/:workflowId',
  auth('delete_novu_workflows'),
  NovuWorkflowController.deleteImportedWorkflow
);

/**
 * @swagger
 * /novu-workflows/export-to-novu:
 *   post:
 *     summary: Export imported workflows to Novu platform
 *     tags: [Novu Workflows]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               workflowNames:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Optional array of workflow names to export. If not provided, all imported workflows will be exported.
 *           example:
 *             workflowNames: ["Welcome Email", "Password Reset"]
 *     responses:
 *       200:
 *         description: Workflows exported to Novu successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Workflows exported to Novu successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     exported:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           workflowId:
 *                             type: string
 *                           name:
 *                             type: string
 *                           action:
 *                             type: string
 *                             enum: [created, updated]
 *                     errors:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           workflowName:
 *                             type: string
 *                           error:
 *                             type: string
 *                     total:
 *                       type: integer
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post(
  '/export-to-novu',
  auth('create_novu_workflow'),
  validate(NovuWorkflowValidation.exportToNovu),
  NovuWorkflowController.exportToNovu
);

/**
 * @swagger
 * /novu-workflows/{workflowId}:
 *   get:
 *     summary: Get a specific Novu workflow
 *     tags: [Novu Workflows]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: workflowId
 *         required: true
 *         schema:
 *           type: string
 *         description: Workflow identifier
 *     responses:
 *       200:
 *         description: Workflow retrieved successfully
 *       404:
 *         description: Workflow not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get(
  '/:workflowId',
  auth('view_novu_workflows'),
  validate(NovuWorkflowValidation.getWorkflow),
  NovuWorkflowController.getWorkflow
);

/**
 * @swagger
 * /novu-workflows/{workflowId}:
 *   put:
 *     summary: Update a Novu workflow
 *     tags: [Novu Workflows]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: workflowId
 *         required: true
 *         schema:
 *           type: string
 *         description: Workflow identifier
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               tags:
 *                 type: array
 *                 items:
 *                   type: string
 *               active:
 *                 type: boolean
 *               steps:
 *                 type: array
 *                 items:
 *                   type: object
 *     responses:
 *       200:
 *         description: Workflow updated successfully
 *       404:
 *         description: Workflow not found
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.put(
  '/:workflowId',
  auth('edit_novu_workflow'),
  validate(NovuWorkflowValidation.updateWorkflow),
  NovuWorkflowController.updateWorkflow
);

/**
 * @swagger
 * /novu-workflows/{workflowId}:
 *   delete:
 *     summary: Delete a Novu workflow
 *     tags: [Novu Workflows]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: workflowId
 *         required: true
 *         schema:
 *           type: string
 *         description: Workflow identifier
 *     responses:
 *       200:
 *         description: Workflow deleted successfully
 *       404:
 *         description: Workflow not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.delete(
  '/:workflowId',
  auth('delete_novu_workflow'),
  validate(NovuWorkflowValidation.deleteWorkflow),
  NovuWorkflowController.deleteWorkflow
);

/**
 * @swagger
 * /novu-workflows/{workflowId}/sync:
 *   put:
 *     summary: Sync a Novu workflow
 *     tags: [Novu Workflows]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: workflowId
 *         required: true
 *         schema:
 *           type: string
 *         description: Workflow identifier
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Workflow synced successfully
 *       404:
 *         description: Workflow not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.put(
  '/:workflowId/sync',
  auth('sync_novu_workflow'),
  validate(NovuWorkflowValidation.syncWorkflow),
  NovuWorkflowController.syncWorkflow
);

module.exports = router;